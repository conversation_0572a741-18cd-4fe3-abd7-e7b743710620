/**
 * API 服务类 - 处理与后端API的通信
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

interface APIResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

interface Keyword {
  id: string;
  keyword: string;
  search_volume: number | null;
  keyword_difficulty: number | null;
  cpc: number | null;
  potential_score: number | null;
  status: string;
  is_approved: boolean;
  created_at: string;
  language: string;
  country: string;
  search_intent?: string;
  trend_score?: number | null;
  serp_quality_score?: number | null;
  tool_category?: string;
}

interface KeywordListResponse {
  keywords: Keyword[];
  total: number;
  page: number;
  size: number;
  total_pages: number;
}

interface Website {
  id: string;
  keyword_id: string;
  keyword: Keyword;
  domain: string;
  template_type: string;
  deployment_status: string;
  github_repo?: string;
  vercel_url?: string;
  lighthouse_score?: number;
  created_at: string;
  deployed_at?: string;
}

interface WebsiteListResponse {
  websites: Website[];
  total: number;
  page: number;
  size: number;
  total_pages: number;
}

class APIService {
  private baseURL: string;

  constructor() {
    this.baseURL = API_BASE_URL;
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<APIResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`;
      const defaultHeaders = {
        'Content-Type': 'application/json',
      };

      const response = await fetch(url, {
        ...options,
        headers: {
          ...defaultHeaders,
          ...options.headers,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          error: errorData.detail || `HTTP ${response.status}: ${response.statusText}`,
        };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      console.error('API Request Error:', error);
      return {
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  // 关键词相关API
  async getKeywords(params: {
    page?: number;
    size?: number;
    status?: string;
    language?: string;
    country?: string;
    search?: string;
    min_volume?: number;
    max_difficulty?: number;
    approved_only?: boolean;
    sort_by?: string;
    sort_order?: string;
  } = {}): Promise<APIResponse<KeywordListResponse>> {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, value.toString());
      }
    });

    return this.makeRequest<KeywordListResponse>(`/api/v1/keywords?${searchParams}`);
  }

  async getKeyword(id: string): Promise<APIResponse<Keyword>> {
    return this.makeRequest<Keyword>(`/api/v1/keywords/${id}`);
  }

  async analyzeKeywords(keywords: string[], language = 'en', country = 'us'): Promise<APIResponse<Keyword[]>> {
    return this.makeRequest<Keyword[]>('/api/v1/keywords/analyze', {
      method: 'POST',
      body: JSON.stringify({
        keywords,
        language,
        country,
      }),
    });
  }

  async approveKeyword(id: string): Promise<APIResponse<{ message: string }>> {
    return this.makeRequest<{ message: string }>(`/api/v1/keywords/${id}/approve`, {
      method: 'POST',
    });
  }

  async rejectKeyword(id: string): Promise<APIResponse<{ message: string }>> {
    return this.makeRequest<{ message: string }>(`/api/v1/keywords/${id}/reject`, {
      method: 'POST',
    });
  }

  async deleteKeyword(id: string): Promise<APIResponse<{ message: string }>> {
    return this.makeRequest<{ message: string }>(`/api/v1/keywords/${id}`, {
      method: 'DELETE',
    });
  }

  // 网站相关API
  async getWebsites(params: {
    page?: number;
    size?: number;
    status?: string;
    keyword_id?: string;
    template_type?: string;
    sort_by?: string;
    sort_order?: string;
  } = {}): Promise<APIResponse<WebsiteListResponse>> {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, value.toString());
      }
    });

    return this.makeRequest<WebsiteListResponse>(`/api/v1/websites?${searchParams}`);
  }

  async getWebsite(id: string): Promise<APIResponse<Website>> {
    return this.makeRequest<Website>(`/api/v1/websites/${id}`);
  }

  async generateWebsite(keywordId: string): Promise<APIResponse<Website>> {
    return this.makeRequest<Website>('/api/v1/websites/generate', {
      method: 'POST',
      body: JSON.stringify({
        keyword_id: keywordId,
      }),
    });
  }

  async deployWebsite(id: string): Promise<APIResponse<{ message: string }>> {
    return this.makeRequest<{ message: string }>(`/api/v1/websites/${id}/deploy`, {
      method: 'POST',
    });
  }

  async deleteWebsite(id: string): Promise<APIResponse<{ message: string }>> {
    return this.makeRequest<{ message: string }>(`/api/v1/websites/${id}`, {
      method: 'DELETE',
    });
  }

  // Dashboard 统计API
  async getDashboardStats(): Promise<APIResponse<{
    total_keywords: number;
    approved_keywords: number;
    total_websites: number;
    deployed_websites: number;
    avg_potential_score: number;
    recent_activity: Array<{
      type: string;
      message: string;
      timestamp: string;
    }>;
  }>> {
    return this.makeRequest('/api/v1/dashboard/stats');
  }
}

export const apiService = new APIService();
export type { Keyword, Website, KeywordListResponse, WebsiteListResponse };