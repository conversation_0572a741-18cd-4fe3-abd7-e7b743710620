{"name": "nextjs-frontend", "version": "0.0.3", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "generate-client": "openapi-ts", "test": "jest", "coverage": "jest --coverage", "prettier": "prettier --write '**/*.{js,jsx,ts,tsx,json,css,html}'", "tsc": "tsc"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hey-api/client-axios": "^0.3.4", "@hey-api/client-fetch": "^0.4.0", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dotenv": "^16.4.5", "lucide-react": "^0.452.0", "next": "15.1.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.54.0", "react-icons": "^5.4.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.18.0", "@hey-api/openapi-ts": "^0.60.1", "@next/eslint-plugin-next": "^15.1.4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1", "@typescript-eslint/eslint-plugin": "^8.20.0", "autoprefixer": "^10.4.20", "chokidar": "^4.0.1", "chokidar-cli": "^3.0.0", "eslint": "^9.18.0", "eslint-config-next": "15.0.3", "eslint-config-prettier": "^10.0.1", "fork-ts-checker-webpack-plugin": "^9.0.2", "globals": "^15.14.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.47", "prettier": "^3.3.3", "tailwindcss": "^3.4.13", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5", "typescript-eslint": "^8.20.0"}, "overrides": {"@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"}}