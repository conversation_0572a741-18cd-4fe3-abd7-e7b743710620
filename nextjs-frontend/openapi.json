{"openapi": "3.1.0", "info": {"title": "FastAPI", "version": "0.1.0"}, "paths": {"/auth/jwt/login": {"post": {"tags": ["auth"], "summary": "Auth:<PERSON><PERSON><PERSON><PERSON><PERSON>", "operationId": "auth:jwt.login", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/login"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BearerResponse"}, "example": {"access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoiOTIyMWZmYzktNjQwZi00MzcyLTg2ZDMtY2U2NDJjYmE1NjAzIiwiYXVkIjoiZmFzdGFwaS11c2VyczphdXRoIiwiZXhwIjoxNTcxNTA0MTkzfQ.M10bjOe45I5Ncu_uXvOmVV8QxnL-nZfcH96U90JaocI", "token_type": "bearer"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}, "examples": {"LOGIN_BAD_CREDENTIALS": {"summary": "Bad credentials or the user is inactive.", "value": {"detail": "LOGIN_BAD_CREDENTIALS"}}, "LOGIN_USER_NOT_VERIFIED": {"summary": "The user is not verified.", "value": {"detail": "LOGIN_USER_NOT_VERIFIED"}}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/jwt/logout": {"post": {"tags": ["auth"], "summary": "Auth:<PERSON><PERSON><PERSON><PERSON>", "operationId": "auth:jwt.logout", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "401": {"description": "Missing token or inactive user."}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/auth/register": {"post": {"tags": ["auth"], "summary": "Register:Register", "operationId": "register:register", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRead"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}, "examples": {"REGISTER_USER_ALREADY_EXISTS": {"summary": "A user with this email already exists.", "value": {"detail": "REGISTER_USER_ALREADY_EXISTS"}}, "REGISTER_INVALID_PASSWORD": {"summary": "Password validation failed.", "value": {"detail": {"code": "REGISTER_INVALID_PASSWORD", "reason": "Password should beat least 3 characters"}}}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/forgot-password": {"post": {"tags": ["auth"], "summary": "Reset:Forgot Password", "operationId": "reset:forgot_password", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_auth-reset_forgot_password"}}}, "required": true}, "responses": {"202": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/reset-password": {"post": {"tags": ["auth"], "summary": "Reset:Reset Password", "operationId": "reset:reset_password", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_auth-reset_reset_password"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}, "examples": {"RESET_PASSWORD_BAD_TOKEN": {"summary": "Bad or expired token.", "value": {"detail": "RESET_PASSWORD_BAD_TOKEN"}}, "RESET_PASSWORD_INVALID_PASSWORD": {"summary": "Password validation failed.", "value": {"detail": {"code": "RESET_PASSWORD_INVALID_PASSWORD", "reason": "Password should be at least 3 characters"}}}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/request-verify-token": {"post": {"tags": ["auth"], "summary": "Verify:Request-Token", "operationId": "verify:request-token", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_auth-verify_request-token"}}}, "required": true}, "responses": {"202": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/verify": {"post": {"tags": ["auth"], "summary": "Verify:Verify", "operationId": "verify:verify", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_auth-verify_verify"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRead"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}, "examples": {"VERIFY_USER_BAD_TOKEN": {"summary": "Bad token, not existing user ornot the e-mail currently set for the user.", "value": {"detail": "VERIFY_USER_BAD_TOKEN"}}, "VERIFY_USER_ALREADY_VERIFIED": {"summary": "The user is already verified.", "value": {"detail": "VERIFY_USER_ALREADY_VERIFIED"}}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/users/me": {"get": {"tags": ["users"], "summary": "Users:Current User", "operationId": "users:current_user", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRead"}}}}, "401": {"description": "Missing token or inactive user."}}, "security": [{"OAuth2PasswordBearer": []}]}, "patch": {"tags": ["users"], "summary": "Users:Patch Current User", "operationId": "users:patch_current_user", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRead"}}}}, "401": {"description": "Missing token or inactive user."}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}, "examples": {"UPDATE_USER_EMAIL_ALREADY_EXISTS": {"summary": "A user with this email already exists.", "value": {"detail": "UPDATE_USER_EMAIL_ALREADY_EXISTS"}}, "UPDATE_USER_INVALID_PASSWORD": {"summary": "Password validation failed.", "value": {"detail": {"code": "UPDATE_USER_INVALID_PASSWORD", "reason": "Password should beat least 3 characters"}}}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/users/{id}": {"get": {"tags": ["users"], "summary": "Users:User", "operationId": "users:user", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRead"}}}}, "401": {"description": "Missing token or inactive user."}, "403": {"description": "Not a superuser."}, "404": {"description": "The user does not exist."}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["users"], "summary": "Users:Patch User", "operationId": "users:patch_user", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "title": "Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRead"}}}}, "401": {"description": "Missing token or inactive user."}, "403": {"description": "Not a superuser."}, "404": {"description": "The user does not exist."}, "400": {"content": {"application/json": {"examples": {"UPDATE_USER_EMAIL_ALREADY_EXISTS": {"summary": "A user with this email already exists.", "value": {"detail": "UPDATE_USER_EMAIL_ALREADY_EXISTS"}}, "UPDATE_USER_INVALID_PASSWORD": {"summary": "Password validation failed.", "value": {"detail": {"code": "UPDATE_USER_INVALID_PASSWORD", "reason": "Password should beat least 3 characters"}}}}, "schema": {"$ref": "#/components/schemas/ErrorModel"}}}, "description": "Bad Request"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["users"], "summary": "Users:Delete User", "operationId": "users:delete_user", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "title": "Id"}}], "responses": {"204": {"description": "Successful Response"}, "401": {"description": "Missing token or inactive user."}, "403": {"description": "Not a superuser."}, "404": {"description": "The user does not exist."}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/items/": {"get": {"tags": ["item"], "summary": "Read Item", "operationId": "read_item", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ItemRead"}, "type": "array", "title": "Response Item-Read Item"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}, "post": {"tags": ["item"], "summary": "Create <PERSON><PERSON>", "operationId": "create_item", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/items/{item_id}": {"delete": {"tags": ["item"], "summary": "Delete Item", "operationId": "delete_item", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "item_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Item Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/keywords/": {"post": {"tags": ["keywords"], "summary": "Create Keyword", "description": "创建新关键词", "operationId": "create_keyword", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KeywordCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KeywordResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["keywords"], "summary": "List Keywords", "description": "获取关键词列表", "operationId": "list_keywords", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Size"}, "description": "每页数量"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "状态筛选", "title": "Status"}, "description": "状态筛选"}, {"name": "language", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "语言筛选", "title": "Language"}, "description": "语言筛选"}, {"name": "country", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "国家筛选", "title": "Country"}, "description": "国家筛选"}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "关键词搜索", "title": "Search"}, "description": "关键词搜索"}, {"name": "min_volume", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "minimum": 0}, {"type": "null"}], "description": "最小搜索量", "title": "Min Volume"}, "description": "最小搜索量"}, {"name": "max_difficulty", "in": "query", "required": false, "schema": {"anyOf": [{"type": "number", "maximum": 100.0, "minimum": 0.0}, {"type": "null"}], "description": "最大难度", "title": "<PERSON>"}, "description": "最大难度"}, {"name": "approved_only", "in": "query", "required": false, "schema": {"type": "boolean", "description": "仅显示已批准的", "default": false, "title": "Approved Only"}, "description": "仅显示已批准的"}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "description": "排序字段", "default": "created_at", "title": "Sort By"}, "description": "排序字段"}, {"name": "sort_order", "in": "query", "required": false, "schema": {"type": "string", "description": "排序方向", "default": "desc", "title": "Sort Order"}, "description": "排序方向"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KeywordListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/keywords/{keyword_id}": {"get": {"tags": ["keywords"], "summary": "Get Keyword", "description": "获取单个关键词详情", "operationId": "get_keyword", "parameters": [{"name": "keyword_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Keyword Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KeywordResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["keywords"], "summary": "Update Keyword", "description": "更新关键词", "operationId": "update_keyword", "parameters": [{"name": "keyword_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Keyword Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KeywordUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KeywordResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["keywords"], "summary": "Delete Keyword", "description": "删除关键词", "operationId": "delete_keyword", "parameters": [{"name": "keyword_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Keyword Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/keywords/analyze": {"post": {"tags": ["keywords"], "summary": "Analyze Keywords", "description": "分析关键词并存储到数据库", "operationId": "analyze_keywords", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/KeywordAnalysisRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/KeywordResponse"}, "type": "array", "title": "Response Keywords-Analyze Keywords"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/keywords/{keyword_id}/calculate-score": {"post": {"tags": ["keywords"], "summary": "Calculate Potential Score", "description": "重新计算关键词潜力分数", "operationId": "calculate_potential_score", "parameters": [{"name": "keyword_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Keyword Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PotentialScoreRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/keywords/{keyword_id}/approve": {"post": {"tags": ["keywords"], "summary": "Approve Keyword", "description": "批准关键词用于建站", "operationId": "approve_keyword", "parameters": [{"name": "keyword_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Keyword Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/keywords/{keyword_id}/reject": {"post": {"tags": ["keywords"], "summary": "Reject Keyword", "description": "拒绝关键词", "operationId": "reject_keyword", "parameters": [{"name": "keyword_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Keyword Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/keywords/seeds": {"post": {"tags": ["keywords"], "summary": "Create Seed Keyword", "description": "创建种子关键词", "operationId": "create_seed_keyword", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SeedKeywordCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SeedKeywordResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["keywords"], "summary": "List Seed Keywords", "description": "获取种子关键词列表", "operationId": "list_seed_keywords", "parameters": [{"name": "active_only", "in": "query", "required": false, "schema": {"type": "boolean", "description": "仅显示激活的种子词", "default": true, "title": "Active Only"}, "description": "仅显示激活的种子词"}, {"name": "category", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "分类筛选", "title": "Category"}, "description": "分类筛选"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SeedKeywordResponse"}, "title": "Response Keywords-List Seed Keywords"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/keywords/seeds/{seed_id}/expand": {"post": {"tags": ["keywords"], "summary": "Expand Seed Keyword", "description": "扩展种子关键词生成相关关键词", "operationId": "expand_seed_keyword", "parameters": [{"name": "seed_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Seed Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/websites/": {"post": {"tags": ["websites"], "summary": "Create Website", "description": "创建新网站", "operationId": "create_website", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebsiteCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebsiteResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["websites"], "summary": "List Websites", "description": "获取网站列表", "operationId": "list_websites", "parameters": [{"name": "keyword_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Keyword Id"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 50, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WebsiteResponse"}, "title": "Response Websites-List Websites"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/websites/{website_id}": {"get": {"tags": ["websites"], "summary": "Get Website", "description": "获取单个网站详情", "operationId": "get_website", "parameters": [{"name": "website_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Website Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebsiteResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["websites"], "summary": "Update Website", "description": "更新网站", "operationId": "update_website", "parameters": [{"name": "website_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Website Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebsiteUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebsiteResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["websites"], "summary": "Delete Website", "description": "删除网站", "operationId": "delete_website", "parameters": [{"name": "website_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Website Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/websites/{website_id}/regenerate": {"post": {"tags": ["websites"], "summary": "Regenerate Website", "description": "重新生成网站", "operationId": "regenerate_website", "parameters": [{"name": "website_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Website Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/websites/{website_id}/deploy": {"post": {"tags": ["websites"], "summary": "Deploy Website", "description": "部署网站", "operationId": "deploy_website", "parameters": [{"name": "website_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Website Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/websites/{website_id}/tasks": {"get": {"tags": ["websites"], "summary": "Get Website Tasks", "description": "获取网站任务列表", "operationId": "get_website_tasks", "parameters": [{"name": "website_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Website Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WebsiteTaskResponse"}, "title": "Response Websites-Get Website Tasks"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/websites/batch-generate": {"post": {"tags": ["websites"], "summary": "Batch Generate Websites", "description": "批量生成网站", "operationId": "batch_generate_websites", "parameters": [{"name": "template_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Template Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}, "title": "Keyword Ids"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"BearerResponse": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "token_type": {"type": "string", "title": "Token Type"}}, "type": "object", "required": ["access_token", "token_type"], "title": "BearerResponse"}, "Body_auth-reset_forgot_password": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}}, "type": "object", "required": ["email"], "title": "Body_auth-reset:forgot_password"}, "Body_auth-reset_reset_password": {"properties": {"token": {"type": "string", "title": "Token"}, "password": {"type": "string", "title": "Password"}}, "type": "object", "required": ["token", "password"], "title": "Body_auth-reset:reset_password"}, "Body_auth-verify_request-token": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}}, "type": "object", "required": ["email"], "title": "Body_auth-verify:request-token"}, "Body_auth-verify_verify": {"properties": {"token": {"type": "string", "title": "Token"}}, "type": "object", "required": ["token"], "title": "Body_auth-verify:verify"}, "ErrorModel": {"properties": {"detail": {"anyOf": [{"type": "string"}, {"additionalProperties": {"type": "string"}, "type": "object"}], "title": "Detail"}}, "type": "object", "required": ["detail"], "title": "ErrorModel"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ItemCreate": {"properties": {"name": {"type": "string", "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "quantity": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Quantity"}}, "type": "object", "required": ["name"], "title": "ItemCreate"}, "ItemRead": {"properties": {"name": {"type": "string", "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "quantity": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Quantity"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "user_id": {"type": "string", "format": "uuid", "title": "User Id"}}, "type": "object", "required": ["name", "id", "user_id"], "title": "ItemRead"}, "KeywordAnalysisRequest": {"properties": {"keywords": {"items": {"type": "string"}, "type": "array", "maxItems": 100, "minItems": 1, "title": "Keywords", "description": "关键词列表"}, "language": {"type": "string", "maxLength": 10, "title": "Language", "description": "语言代码", "default": "en"}, "country": {"type": "string", "maxLength": 10, "title": "Country", "description": "国家代码", "default": "us"}, "include_serp": {"type": "boolean", "title": "Include Serp", "description": "是否包含SERP分析", "default": true}, "include_trends": {"type": "boolean", "title": "Include Trends", "description": "是否包含趋势分析", "default": true}}, "type": "object", "required": ["keywords"], "title": "KeywordAnalysisRequest", "description": "关键词分析请求"}, "KeywordCreate": {"properties": {"keyword": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Keyword", "description": "关键词"}, "language": {"type": "string", "maxLength": 10, "title": "Language", "description": "语言代码", "default": "en"}, "country": {"type": "string", "maxLength": 10, "title": "Country", "description": "国家代码", "default": "us"}, "search_volume": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Search Volume", "description": "月搜索量"}, "keyword_difficulty": {"anyOf": [{"type": "number", "maximum": 100.0, "minimum": 0.0}, {"type": "null"}], "title": "Keyword Difficulty", "description": "关键词难度"}, "cpc": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Cpc", "description": "每次点击成本"}, "competition": {"anyOf": [{"type": "number", "maximum": 1.0, "minimum": 0.0}, {"type": "null"}], "title": "Competition", "description": "竞争度"}, "search_intent": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Search Intent", "description": "搜索意图"}, "zero_click_rate": {"anyOf": [{"type": "number", "maximum": 1.0, "minimum": 0.0}, {"type": "null"}], "title": "Zero Click Rate", "description": "零点击率"}, "tool_complexity": {"anyOf": [{"type": "integer", "maximum": 5.0, "minimum": 1.0}, {"type": "null"}], "title": "Tool Complexity", "description": "工具复杂度"}, "tool_category": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Tool Category", "description": "工具分类"}}, "type": "object", "required": ["keyword"], "title": "KeywordCreate"}, "KeywordListResponse": {"properties": {"keywords": {"items": {"$ref": "#/components/schemas/KeywordResponse"}, "type": "array", "title": "Keywords"}, "total": {"type": "integer", "title": "Total"}, "page": {"type": "integer", "title": "Page"}, "size": {"type": "integer", "title": "Size"}, "total_pages": {"type": "integer", "title": "Total Pages"}}, "type": "object", "required": ["keywords", "total", "page", "size", "total_pages"], "title": "KeywordListResponse"}, "KeywordResponse": {"properties": {"keyword": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Keyword", "description": "关键词"}, "language": {"type": "string", "maxLength": 10, "title": "Language", "description": "语言代码", "default": "en"}, "country": {"type": "string", "maxLength": 10, "title": "Country", "description": "国家代码", "default": "us"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "search_volume": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Search Volume"}, "keyword_difficulty": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Keyword Difficulty"}, "cpc": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Cpc"}, "competition": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Competition"}, "search_intent": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search Intent"}, "zero_click_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Zero Click Rate"}, "trend_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Trend Score"}, "trend_data": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Trend Data"}, "serp_quality_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Serp Quality Score"}, "serp_analysis": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Serp Analysis"}, "tool_complexity": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tool Complexity"}, "tool_category": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tool Category"}, "potential_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Potential Score"}, "status": {"type": "string", "title": "Status"}, "is_approved": {"type": "boolean", "title": "Is Approved"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "last_checked_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Checked At"}, "next_check_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Next Check At"}}, "type": "object", "required": ["keyword", "id", "status", "is_approved", "created_at", "updated_at"], "title": "KeywordResponse"}, "KeywordUpdate": {"properties": {"search_volume": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Search Volume"}, "keyword_difficulty": {"anyOf": [{"type": "number", "maximum": 100.0, "minimum": 0.0}, {"type": "null"}], "title": "Keyword Difficulty"}, "cpc": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Cpc"}, "competition": {"anyOf": [{"type": "number", "maximum": 1.0, "minimum": 0.0}, {"type": "null"}], "title": "Competition"}, "search_intent": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Search Intent"}, "zero_click_rate": {"anyOf": [{"type": "number", "maximum": 1.0, "minimum": 0.0}, {"type": "null"}], "title": "Zero Click Rate"}, "trend_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Trend Score"}, "trend_data": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Trend Data"}, "serp_quality_score": {"anyOf": [{"type": "number", "maximum": 1.0, "minimum": 0.0}, {"type": "null"}], "title": "Serp Quality Score"}, "serp_analysis": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Serp Analysis"}, "tool_complexity": {"anyOf": [{"type": "integer", "maximum": 5.0, "minimum": 1.0}, {"type": "null"}], "title": "Tool Complexity"}, "tool_category": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Tool Category"}, "potential_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Potential Score"}, "status": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Status"}, "is_approved": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Approved"}}, "type": "object", "title": "KeywordUpdate"}, "PotentialScoreRequest": {"properties": {"volume_weight": {"type": "number", "maximum": 1.0, "minimum": 0.0, "title": "Volume Weight", "description": "搜索量权重", "default": 0.3}, "difficulty_weight": {"type": "number", "maximum": 1.0, "minimum": 0.0, "title": "Difficulty Weight", "description": "难度权重", "default": 0.3}, "cpc_weight": {"type": "number", "maximum": 1.0, "minimum": 0.0, "title": "Cpc Weight", "description": "CPC权重", "default": 0.2}, "trend_weight": {"type": "number", "maximum": 1.0, "minimum": 0.0, "title": "Trend Weight", "description": "趋势权重", "default": 0.1}, "serp_weight": {"type": "number", "maximum": 1.0, "minimum": 0.0, "title": "Serp Weight", "description": "SERP质量权重", "default": 0.1}}, "type": "object", "title": "PotentialScoreRequest", "description": "潜力评分计算请求"}, "SeedKeywordCreate": {"properties": {"root_word": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Root Word", "description": "词根"}, "category": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Category", "description": "分类"}, "priority": {"type": "integer", "maximum": 10.0, "minimum": 1.0, "title": "Priority", "description": "优先级", "default": 1}, "language": {"type": "string", "maxLength": 10, "title": "Language", "description": "语言代码", "default": "en"}, "country": {"type": "string", "maxLength": 10, "title": "Country", "description": "国家代码", "default": "us"}, "modifiers": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Modifiers", "description": "修饰词列表"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "是否激活", "default": true}}, "type": "object", "required": ["root_word"], "title": "SeedKeywordCreate"}, "SeedKeywordResponse": {"properties": {"root_word": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Root Word", "description": "词根"}, "category": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Category", "description": "分类"}, "priority": {"type": "integer", "maximum": 10.0, "minimum": 1.0, "title": "Priority", "description": "优先级", "default": 1}, "language": {"type": "string", "maxLength": 10, "title": "Language", "description": "语言代码", "default": "en"}, "country": {"type": "string", "maxLength": 10, "title": "Country", "description": "国家代码", "default": "us"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "modifiers": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Modifiers"}, "is_active": {"type": "boolean", "title": "Is Active"}, "last_expanded_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Expanded At"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["root_word", "id", "is_active", "created_at", "updated_at"], "title": "SeedKeywordResponse"}, "UserCreate": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "password": {"type": "string", "title": "Password"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active", "default": true}, "is_superuser": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Superuser", "default": false}, "is_verified": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Verified", "default": false}}, "type": "object", "required": ["email", "password"], "title": "UserCreate"}, "UserRead": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "email": {"type": "string", "format": "email", "title": "Email"}, "is_active": {"type": "boolean", "title": "Is Active", "default": true}, "is_superuser": {"type": "boolean", "title": "Is Superuser", "default": false}, "is_verified": {"type": "boolean", "title": "Is Verified", "default": false}}, "type": "object", "required": ["id", "email"], "title": "UserRead"}, "UserUpdate": {"properties": {"password": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Password"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}, "is_superuser": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Superuser"}, "is_verified": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Verified"}}, "type": "object", "title": "UserUpdate"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "WebsiteCreate": {"properties": {"title": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Title", "description": "网站标题"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "网站描述"}, "keyword_id": {"type": "string", "format": "uuid", "title": "Keyword Id", "description": "关联的关键词ID"}, "domain": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Domain", "description": "域名"}, "subdomain": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Subdomain", "description": "子域名"}, "template_id": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Template Id", "description": "模板ID"}}, "type": "object", "required": ["title", "keyword_id"], "title": "WebsiteCreate"}, "WebsiteResponse": {"properties": {"title": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Title", "description": "网站标题"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "网站描述"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "keyword_id": {"type": "string", "format": "uuid", "title": "Keyword Id"}, "domain": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Domain"}, "subdomain": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Subdomain"}, "content_data": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Content Data"}, "seo_metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "template_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Template Id"}, "github_repo": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "deployment_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deployment Url"}, "status": {"type": "string", "title": "Status"}, "build_log": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Build Log"}, "lighthouse_score": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Lighthouse Score"}, "page_speed": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "<PERSON>"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "deployed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Deployed At"}}, "type": "object", "required": ["title", "id", "keyword_id", "status", "created_at", "updated_at"], "title": "WebsiteResponse"}, "WebsiteTaskResponse": {"properties": {"task_type": {"type": "string", "maxLength": 50, "title": "Task Type", "description": "任务类型"}, "task_config": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Task Config", "description": "任务配置"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "task_status": {"type": "string", "title": "Task Status"}, "celery_task_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Celery Task Id"}, "result_data": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Result Data"}, "error_message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error Message"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "started_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Started At"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At"}, "website_id": {"type": "string", "format": "uuid", "title": "Website Id"}}, "type": "object", "required": ["task_type", "id", "task_status", "created_at", "website_id"], "title": "WebsiteTaskResponse"}, "WebsiteUpdate": {"properties": {"title": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Title"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "domain": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Domain"}, "subdomain": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Subdomain"}, "content_data": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Content Data"}, "seo_metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "template_id": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Template Id"}, "github_repo": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "deployment_url": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Deployment Url"}, "status": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Status"}, "build_log": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Build Log"}, "lighthouse_score": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Lighthouse Score"}, "page_speed": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "<PERSON>"}}, "type": "object", "title": "WebsiteUpdate"}, "login": {"properties": {"grant_type": {"anyOf": [{"type": "string", "pattern": "password"}, {"type": "null"}], "title": "Grant Type"}, "username": {"type": "string", "title": "Username"}, "password": {"type": "string", "title": "Password"}, "scope": {"type": "string", "title": "<PERSON><PERSON>", "default": ""}, "client_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Id"}, "client_secret": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Secret"}}, "type": "object", "required": ["username", "password"], "title": "Body_auth-auth:jwt.login"}}, "securitySchemes": {"OAuth2PasswordBearer": {"type": "oauth2", "flows": {"password": {"scopes": {}, "tokenUrl": "auth/jwt/login"}}}}}}