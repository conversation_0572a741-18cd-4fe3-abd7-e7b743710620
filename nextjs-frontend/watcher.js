const chokidar = require("chokidar");
const { exec } = require("child_process");
const { config } = require("dotenv");
const path = require("path");

config({ path: ".env.local" });

const openapiFile = process.env.OPENAPI_OUTPUT_FILE || "openapi.json";

// Validate that the OpenAPI file path is a string
if (typeof openapiFile !== "string" || openapiFile.trim() === "") {
  console.error("Error: OPENAPI_OUTPUT_FILE must be a non-empty string");
  console.error("Current value:", openapiFile);
  console.error("Please check your .env.local file");
  process.exit(1);
}

// Resolve the full path to ensure it exists
const fullPath = path.resolve(openapiFile);
console.log(`Watching OpenAPI file: ${fullPath}`);

// Watch the specific file for changes
chokidar.watch(openapiFile).on("change", (filePath) => {
  console.log(`File ${filePath} has been modified. Running generate-client...`);
  exec("pnpm run generate-client", (error, stdout, stderr) => {
    if (error) {
      console.error(`Error: ${error.message}`);
      return;
    }
    if (stderr) {
      console.error(`stderr: ${stderr}`);
      return;
    }
    console.log(`stdout: ${stdout}`);
  });
}).on("error", (error) => {
  console.error(`Watcher error: ${error}`);
}).on("ready", () => {
  console.log("File watcher is ready and watching for changes...");
});
