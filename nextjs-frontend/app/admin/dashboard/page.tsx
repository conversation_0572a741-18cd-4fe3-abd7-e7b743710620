"use client";

import { useState, useEffect } from "react";

interface DashboardStats {
  totalKeywords: number;
  totalWebsites: number;
  activeDeployments: number;
  successRate: number;
  recentKeywords: Array<{
    id: string;
    keyword: string;
    potential_score: number;
    status: string;
    created_at: string;
  }>;
  recentWebsites: Array<{
    id: string;
    title: string;
    status: string;
    deployment_url?: string;
    created_at: string;
  }>;
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setStats({
        totalKeywords: 1234,
        totalWebsites: 567,
        activeDeployments: 45,
        successRate: 85.6,
        recentKeywords: [
          {
            id: "1",
            keyword: "online pdf converter",
            potential_score: 0.87,
            status: "evaluated",
            created_at: "2024-01-15T10:30:00Z"
          },
          {
            id: "2", 
            keyword: "image resizer tool",
            potential_score: 0.76,
            status: "approved",
            created_at: "2024-01-15T09:15:00Z"
          },
          {
            id: "3",
            keyword: "text to speech generator",
            potential_score: 0.92,
            status: "evaluated",
            created_at: "2024-01-14T16:45:00Z"
          }
        ],
        recentWebsites: [
          {
            id: "1",
            title: "PDF Converter Tool",
            status: "deployed",
            deployment_url: "https://pdf-converter-tool.vercel.app",
            created_at: "2024-01-15T11:00:00Z"
          },
          {
            id: "2",
            title: "Image Resizer",
            status: "building",
            created_at: "2024-01-15T10:45:00Z"
          }
        ]
      });
    } catch (error) {
      console.error("Failed to fetch dashboard stats:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Failed to load dashboard data</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">控制面板</h1>
        <p className="mt-2 text-gray-600">关键词分析和网站生成概览</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <span className="text-white font-semibold">K</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">关键词总数</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.totalKeywords.toLocaleString()}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <span className="text-white font-semibold">W</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">网站总数</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.totalWebsites.toLocaleString()}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                  <span className="text-white font-semibold">D</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Active Deployments</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.activeDeployments}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                  <span className="text-white font-semibold">%</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Success Rate</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.successRate}%</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Recent Keywords */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Recent Keywords</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {stats.recentKeywords.map((keyword) => (
                <div key={keyword.id} className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{keyword.keyword}</p>
                    <p className="text-sm text-gray-500">
                      Score: {(keyword.potential_score * 100).toFixed(1)}% | Status: {keyword.status}
                    </p>
                  </div>
                  <div className="ml-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      keyword.status === "approved" 
                        ? "bg-green-100 text-green-800"
                        : keyword.status === "evaluated"
                        ? "bg-blue-100 text-blue-800"
                        : "bg-gray-100 text-gray-800"
                    }`}>
                      {keyword.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Websites */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Recent Websites</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {stats.recentWebsites.map((website) => (
                <div key={website.id} className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{website.title}</p>
                    <p className="text-sm text-gray-500">
                      {website.deployment_url ? (
                        <a 
                          href={website.deployment_url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800"
                        >
                          {website.deployment_url}
                        </a>
                      ) : (
                        "Building..."
                      )}
                    </p>
                  </div>
                  <div className="ml-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      website.status === "deployed" 
                        ? "bg-green-100 text-green-800"
                        : website.status === "building"
                        ? "bg-yellow-100 text-yellow-800"
                        : "bg-gray-100 text-gray-800"
                    }`}>
                      {website.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <button className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
              Analyze Keywords
            </button>
            <button className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
              Generate Website
            </button>
            <button className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
              Deploy Batch
            </button>
            <button className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-600 hover:bg-gray-700">
              View Reports
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}