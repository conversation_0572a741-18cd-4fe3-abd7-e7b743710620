// This file is auto-generated by @hey-api/openapi-ts

export type BearerResponse = {
  access_token: string;
  token_type: string;
};

export type Body_auth_reset_forgot_password = {
  email: string;
};

export type Body_auth_reset_reset_password = {
  token: string;
  password: string;
};

export type Body_auth_verify_request_token = {
  email: string;
};

export type Body_auth_verify_verify = {
  token: string;
};

export type ErrorModel = {
  detail:
    | string
    | {
        [key: string]: string;
      };
};

export type HTTPValidationError = {
  detail?: Array<ValidationError>;
};

export type ItemCreate = {
  name: string;
  description?: string | null;
  quantity?: number | null;
};

export type ItemRead = {
  name: string;
  description?: string | null;
  quantity?: number | null;
  id: string;
  user_id: string;
};

/**
 * 关键词分析请求
 */
export type KeywordAnalysisRequest = {
  /**
   * 关键词列表
   */
  keywords: Array<string>;
  /**
   * 语言代码
   */
  language?: string;
  /**
   * 国家代码
   */
  country?: string;
  /**
   * 是否包含SERP分析
   */
  include_serp?: boolean;
  /**
   * 是否包含趋势分析
   */
  include_trends?: boolean;
};

export type KeywordCreate = {
  /**
   * 关键词
   */
  keyword: string;
  /**
   * 语言代码
   */
  language?: string;
  /**
   * 国家代码
   */
  country?: string;
  /**
   * 月搜索量
   */
  search_volume?: number | null;
  /**
   * 关键词难度
   */
  keyword_difficulty?: number | null;
  /**
   * 每次点击成本
   */
  cpc?: number | null;
  /**
   * 竞争度
   */
  competition?: number | null;
  /**
   * 搜索意图
   */
  search_intent?: string | null;
  /**
   * 零点击率
   */
  zero_click_rate?: number | null;
  /**
   * 工具复杂度
   */
  tool_complexity?: number | null;
  /**
   * 工具分类
   */
  tool_category?: string | null;
};

export type KeywordListResponse = {
  keywords: Array<KeywordResponse>;
  total: number;
  page: number;
  size: number;
  total_pages: number;
};

export type KeywordResponse = {
  /**
   * 关键词
   */
  keyword: string;
  /**
   * 语言代码
   */
  language?: string;
  /**
   * 国家代码
   */
  country?: string;
  id: string;
  search_volume?: number | null;
  keyword_difficulty?: number | null;
  cpc?: number | null;
  competition?: number | null;
  search_intent?: string | null;
  zero_click_rate?: number | null;
  trend_score?: number | null;
  trend_data?: {
    [key: string]: unknown;
  } | null;
  serp_quality_score?: number | null;
  serp_analysis?: {
    [key: string]: unknown;
  } | null;
  tool_complexity?: number | null;
  tool_category?: string | null;
  potential_score?: number | null;
  status: string;
  is_approved: boolean;
  created_at: string;
  updated_at: string;
  last_checked_at?: string | null;
  next_check_at?: string | null;
};

export type KeywordUpdate = {
  search_volume?: number | null;
  keyword_difficulty?: number | null;
  cpc?: number | null;
  competition?: number | null;
  search_intent?: string | null;
  zero_click_rate?: number | null;
  trend_score?: number | null;
  trend_data?: {
    [key: string]: unknown;
  } | null;
  serp_quality_score?: number | null;
  serp_analysis?: {
    [key: string]: unknown;
  } | null;
  tool_complexity?: number | null;
  tool_category?: string | null;
  potential_score?: number | null;
  status?: string | null;
  is_approved?: boolean | null;
};

export type login = {
  grant_type?: string | null;
  username: string;
  password: string;
  scope?: string;
  client_id?: string | null;
  client_secret?: string | null;
};

/**
 * 潜力评分计算请求
 */
export type PotentialScoreRequest = {
  /**
   * 搜索量权重
   */
  volume_weight?: number;
  /**
   * 难度权重
   */
  difficulty_weight?: number;
  /**
   * CPC权重
   */
  cpc_weight?: number;
  /**
   * 趋势权重
   */
  trend_weight?: number;
  /**
   * SERP质量权重
   */
  serp_weight?: number;
};

export type SeedKeywordCreate = {
  /**
   * 词根
   */
  root_word: string;
  /**
   * 分类
   */
  category?: string | null;
  /**
   * 优先级
   */
  priority?: number;
  /**
   * 语言代码
   */
  language?: string;
  /**
   * 国家代码
   */
  country?: string;
  /**
   * 修饰词列表
   */
  modifiers?: Array<string> | null;
  /**
   * 是否激活
   */
  is_active?: boolean;
};

export type SeedKeywordResponse = {
  /**
   * 词根
   */
  root_word: string;
  /**
   * 分类
   */
  category?: string | null;
  /**
   * 优先级
   */
  priority?: number;
  /**
   * 语言代码
   */
  language?: string;
  /**
   * 国家代码
   */
  country?: string;
  id: string;
  modifiers?: Array<string> | null;
  is_active: boolean;
  last_expanded_at?: string | null;
  created_at: string;
  updated_at: string;
};

export type UserCreate = {
  email: string;
  password: string;
  is_active?: boolean | null;
  is_superuser?: boolean | null;
  is_verified?: boolean | null;
};

export type UserRead = {
  id: string;
  email: string;
  is_active?: boolean;
  is_superuser?: boolean;
  is_verified?: boolean;
};

export type UserUpdate = {
  password?: string | null;
  email?: string | null;
  is_active?: boolean | null;
  is_superuser?: boolean | null;
  is_verified?: boolean | null;
};

export type ValidationError = {
  loc: Array<string | number>;
  msg: string;
  type: string;
};

export type WebsiteCreate = {
  /**
   * 网站标题
   */
  title: string;
  /**
   * 网站描述
   */
  description?: string | null;
  /**
   * 关联的关键词ID
   */
  keyword_id: string;
  /**
   * 域名
   */
  domain?: string | null;
  /**
   * 子域名
   */
  subdomain?: string | null;
  /**
   * 模板ID
   */
  template_id?: string | null;
};

export type WebsiteResponse = {
  /**
   * 网站标题
   */
  title: string;
  /**
   * 网站描述
   */
  description?: string | null;
  id: string;
  keyword_id: string;
  domain?: string | null;
  subdomain?: string | null;
  content_data?: {
    [key: string]: unknown;
  } | null;
  seo_metadata?: {
    [key: string]: unknown;
  } | null;
  template_id?: string | null;
  github_repo?: string | null;
  deployment_url?: string | null;
  status: string;
  build_log?: string | null;
  lighthouse_score?: {
    [key: string]: unknown;
  } | null;
  page_speed?: number | null;
  created_at: string;
  updated_at: string;
  deployed_at?: string | null;
};

export type WebsiteTaskResponse = {
  /**
   * 任务类型
   */
  task_type: string;
  /**
   * 任务配置
   */
  task_config?: {
    [key: string]: unknown;
  } | null;
  id: string;
  task_status: string;
  celery_task_id?: string | null;
  result_data?: {
    [key: string]: unknown;
  } | null;
  error_message?: string | null;
  created_at: string;
  started_at?: string | null;
  completed_at?: string | null;
  website_id: string;
};

export type WebsiteUpdate = {
  title?: string | null;
  description?: string | null;
  domain?: string | null;
  subdomain?: string | null;
  content_data?: {
    [key: string]: unknown;
  } | null;
  seo_metadata?: {
    [key: string]: unknown;
  } | null;
  template_id?: string | null;
  github_repo?: string | null;
  deployment_url?: string | null;
  status?: string | null;
  build_log?: string | null;
  lighthouse_score?: {
    [key: string]: unknown;
  } | null;
  page_speed?: number | null;
};

export type AuthJwtLoginData = {
  body: login;
};

export type AuthJwtLoginResponse = BearerResponse;

export type AuthJwtLoginError = ErrorModel | HTTPValidationError;

export type AuthJwtLogoutResponse = unknown;

export type AuthJwtLogoutError = unknown;

export type RegisterRegisterData = {
  body: UserCreate;
};

export type RegisterRegisterResponse = UserRead;

export type RegisterRegisterError = ErrorModel | HTTPValidationError;

export type ResetForgotPasswordData = {
  body: Body_auth_reset_forgot_password;
};

export type ResetForgotPasswordResponse = unknown;

export type ResetForgotPasswordError = HTTPValidationError;

export type ResetResetPasswordData = {
  body: Body_auth_reset_reset_password;
};

export type ResetResetPasswordResponse = unknown;

export type ResetResetPasswordError = ErrorModel | HTTPValidationError;

export type VerifyRequestTokenData = {
  body: Body_auth_verify_request_token;
};

export type VerifyRequestTokenResponse = unknown;

export type VerifyRequestTokenError = HTTPValidationError;

export type VerifyVerifyData = {
  body: Body_auth_verify_verify;
};

export type VerifyVerifyResponse = UserRead;

export type VerifyVerifyError = ErrorModel | HTTPValidationError;

export type UsersCurrentUserResponse = UserRead;

export type UsersCurrentUserError = unknown;

export type UsersPatchCurrentUserData = {
  body: UserUpdate;
};

export type UsersPatchCurrentUserResponse = UserRead;

export type UsersPatchCurrentUserError =
  | ErrorModel
  | unknown
  | HTTPValidationError;

export type UsersUserData = {
  path: {
    id: string;
  };
};

export type UsersUserResponse = UserRead;

export type UsersUserError = unknown | HTTPValidationError;

export type UsersPatchUserData = {
  body: UserUpdate;
  path: {
    id: string;
  };
};

export type UsersPatchUserResponse = UserRead;

export type UsersPatchUserError = ErrorModel | unknown | HTTPValidationError;

export type UsersDeleteUserData = {
  path: {
    id: string;
  };
};

export type UsersDeleteUserResponse = void;

export type UsersDeleteUserError = unknown | HTTPValidationError;

export type ReadItemResponse = Array<ItemRead>;

export type ReadItemError = unknown;

export type CreateItemData = {
  body: ItemCreate;
};

export type CreateItemResponse = ItemRead;

export type CreateItemError = HTTPValidationError;

export type DeleteItemData = {
  path: {
    item_id: string;
  };
};

export type DeleteItemResponse = unknown;

export type DeleteItemError = HTTPValidationError;

export type CreateKeywordData = {
  body: KeywordCreate;
};

export type CreateKeywordResponse = KeywordResponse;

export type CreateKeywordError = HTTPValidationError;

export type ListKeywordsData = {
  query?: {
    /**
     * 仅显示已批准的
     */
    approved_only?: boolean;
    /**
     * 国家筛选
     */
    country?: string | null;
    /**
     * 语言筛选
     */
    language?: string | null;
    /**
     * 最大难度
     */
    max_difficulty?: number | null;
    /**
     * 最小搜索量
     */
    min_volume?: number | null;
    /**
     * 页码
     */
    page?: number;
    /**
     * 关键词搜索
     */
    search?: string | null;
    /**
     * 每页数量
     */
    size?: number;
    /**
     * 排序字段
     */
    sort_by?: string;
    /**
     * 排序方向
     */
    sort_order?: string;
    /**
     * 状态筛选
     */
    status?: string | null;
  };
};

export type ListKeywordsResponse = KeywordListResponse;

export type ListKeywordsError = HTTPValidationError;

export type GetKeywordData = {
  path: {
    keyword_id: string;
  };
};

export type GetKeywordResponse = KeywordResponse;

export type GetKeywordError = HTTPValidationError;

export type UpdateKeywordData = {
  body: KeywordUpdate;
  path: {
    keyword_id: string;
  };
};

export type UpdateKeywordResponse = KeywordResponse;

export type UpdateKeywordError = HTTPValidationError;

export type DeleteKeywordData = {
  path: {
    keyword_id: string;
  };
};

export type DeleteKeywordResponse = unknown;

export type DeleteKeywordError = HTTPValidationError;

export type AnalyzeKeywordsData = {
  body: KeywordAnalysisRequest;
};

export type AnalyzeKeywordsResponse = Array<KeywordResponse>;

export type AnalyzeKeywordsError = HTTPValidationError;

export type CalculatePotentialScoreData = {
  body: PotentialScoreRequest;
  path: {
    keyword_id: string;
  };
};

export type CalculatePotentialScoreResponse = unknown;

export type CalculatePotentialScoreError = HTTPValidationError;

export type ApproveKeywordData = {
  path: {
    keyword_id: string;
  };
};

export type ApproveKeywordResponse = unknown;

export type ApproveKeywordError = HTTPValidationError;

export type RejectKeywordData = {
  path: {
    keyword_id: string;
  };
};

export type RejectKeywordResponse = unknown;

export type RejectKeywordError = HTTPValidationError;

export type CreateSeedKeywordData = {
  body: SeedKeywordCreate;
};

export type CreateSeedKeywordResponse = SeedKeywordResponse;

export type CreateSeedKeywordError = HTTPValidationError;

export type ListSeedKeywordsData = {
  query?: {
    /**
     * 仅显示激活的种子词
     */
    active_only?: boolean;
    /**
     * 分类筛选
     */
    category?: string | null;
  };
};

export type ListSeedKeywordsResponse = Array<SeedKeywordResponse>;

export type ListSeedKeywordsError = HTTPValidationError;

export type ExpandSeedKeywordData = {
  path: {
    seed_id: string;
  };
};

export type ExpandSeedKeywordResponse = unknown;

export type ExpandSeedKeywordError = HTTPValidationError;

export type CreateWebsiteData = {
  body: WebsiteCreate;
};

export type CreateWebsiteResponse = WebsiteResponse;

export type CreateWebsiteError = HTTPValidationError;

export type ListWebsitesData = {
  query?: {
    keyword_id?: string | null;
    limit?: number;
    status?: string | null;
  };
};

export type ListWebsitesResponse = Array<WebsiteResponse>;

export type ListWebsitesError = HTTPValidationError;

export type GetWebsiteData = {
  path: {
    website_id: string;
  };
};

export type GetWebsiteResponse = WebsiteResponse;

export type GetWebsiteError = HTTPValidationError;

export type UpdateWebsiteData = {
  body: WebsiteUpdate;
  path: {
    website_id: string;
  };
};

export type UpdateWebsiteResponse = WebsiteResponse;

export type UpdateWebsiteError = HTTPValidationError;

export type DeleteWebsiteData = {
  path: {
    website_id: string;
  };
};

export type DeleteWebsiteResponse = unknown;

export type DeleteWebsiteError = HTTPValidationError;

export type RegenerateWebsiteData = {
  path: {
    website_id: string;
  };
};

export type RegenerateWebsiteResponse = unknown;

export type RegenerateWebsiteError = HTTPValidationError;

export type DeployWebsiteData = {
  path: {
    website_id: string;
  };
};

export type DeployWebsiteResponse = unknown;

export type DeployWebsiteError = HTTPValidationError;

export type GetWebsiteTasksData = {
  path: {
    website_id: string;
  };
};

export type GetWebsiteTasksResponse = Array<WebsiteTaskResponse>;

export type GetWebsiteTasksError = HTTPValidationError;

export type BatchGenerateWebsitesData = {
  body: Array<string>;
  query?: {
    template_id?: string | null;
  };
};

export type BatchGenerateWebsitesResponse = unknown;

export type BatchGenerateWebsitesError = HTTPValidationError;
