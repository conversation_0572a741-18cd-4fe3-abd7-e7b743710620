// This file is auto-generated by @hey-api/openapi-ts

import {
  createClient,
  createConfig,
  type OptionsLegacyParser,
  urlSearchParamsBodySerializer,
} from "@hey-api/client-axios";
import type {
  AuthJwtLoginData,
  AuthJwtLoginError,
  AuthJwtLoginResponse,
  AuthJwtLogoutError,
  AuthJwtLogoutResponse,
  RegisterRegisterData,
  RegisterRegisterError,
  RegisterRegisterResponse,
  ResetForgotPasswordData,
  ResetForgotPasswordError,
  ResetForgotPasswordResponse,
  ResetResetPasswordData,
  ResetResetPasswordError,
  ResetResetPasswordResponse,
  VerifyRequestTokenData,
  VerifyRequestTokenError,
  VerifyRequestTokenResponse,
  VerifyVerifyData,
  VerifyVerifyError,
  VerifyVerifyResponse,
  UsersCurrentUserError,
  UsersCurrentUserResponse,
  UsersPatchCurrentUserData,
  UsersPatchCurrentUserError,
  UsersPatchCurrentUserResponse,
  UsersUserData,
  UsersUserError,
  UsersUserResponse,
  UsersPatchUserData,
  UsersPatchUserError,
  UsersPatchUserResponse,
  UsersDeleteUserData,
  UsersDeleteUserError,
  UsersDeleteUserResponse,
  ReadItemError,
  ReadItemResponse,
  CreateItemData,
  CreateItemError,
  CreateItemResponse,
  DeleteItemData,
  DeleteItemError,
  DeleteItemResponse,
  CreateKeywordData,
  CreateKeywordError,
  CreateKeywordResponse,
  ListKeywordsData,
  ListKeywordsError,
  ListKeywordsResponse,
  GetKeywordData,
  GetKeywordError,
  GetKeywordResponse,
  UpdateKeywordData,
  UpdateKeywordError,
  UpdateKeywordResponse,
  DeleteKeywordData,
  DeleteKeywordError,
  DeleteKeywordResponse,
  AnalyzeKeywordsData,
  AnalyzeKeywordsError,
  AnalyzeKeywordsResponse,
  CalculatePotentialScoreData,
  CalculatePotentialScoreError,
  CalculatePotentialScoreResponse,
  ApproveKeywordData,
  ApproveKeywordError,
  ApproveKeywordResponse,
  RejectKeywordData,
  RejectKeywordError,
  RejectKeywordResponse,
  CreateSeedKeywordData,
  CreateSeedKeywordError,
  CreateSeedKeywordResponse,
  ListSeedKeywordsData,
  ListSeedKeywordsError,
  ListSeedKeywordsResponse,
  ExpandSeedKeywordData,
  ExpandSeedKeywordError,
  ExpandSeedKeywordResponse,
  CreateWebsiteData,
  CreateWebsiteError,
  CreateWebsiteResponse,
  ListWebsitesData,
  ListWebsitesError,
  ListWebsitesResponse,
  GetWebsiteData,
  GetWebsiteError,
  GetWebsiteResponse,
  UpdateWebsiteData,
  UpdateWebsiteError,
  UpdateWebsiteResponse,
  DeleteWebsiteData,
  DeleteWebsiteError,
  DeleteWebsiteResponse,
  RegenerateWebsiteData,
  RegenerateWebsiteError,
  RegenerateWebsiteResponse,
  DeployWebsiteData,
  DeployWebsiteError,
  DeployWebsiteResponse,
  GetWebsiteTasksData,
  GetWebsiteTasksError,
  GetWebsiteTasksResponse,
  BatchGenerateWebsitesData,
  BatchGenerateWebsitesError,
  BatchGenerateWebsitesResponse,
} from "./types.gen";

export const client = createClient(createConfig());

/**
 * Auth:Jwt.Login
 */
export const authJwtLogin = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<AuthJwtLoginData, ThrowOnError>,
) => {
  return (options?.client ?? client).post<
    AuthJwtLoginResponse,
    AuthJwtLoginError,
    ThrowOnError
  >({
    ...options,
    ...urlSearchParamsBodySerializer,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
      ...options?.headers,
    },
    url: "/auth/jwt/login",
  });
};

/**
 * Auth:Jwt.Logout
 */
export const authJwtLogout = <ThrowOnError extends boolean = false>(
  options?: OptionsLegacyParser<unknown, ThrowOnError>,
) => {
  return (options?.client ?? client).post<
    AuthJwtLogoutResponse,
    AuthJwtLogoutError,
    ThrowOnError
  >({
    ...options,
    url: "/auth/jwt/logout",
  });
};

/**
 * Register:Register
 */
export const registerRegister = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<RegisterRegisterData, ThrowOnError>,
) => {
  return (options?.client ?? client).post<
    RegisterRegisterResponse,
    RegisterRegisterError,
    ThrowOnError
  >({
    ...options,
    url: "/auth/register",
  });
};

/**
 * Reset:Forgot Password
 */
export const resetForgotPassword = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<ResetForgotPasswordData, ThrowOnError>,
) => {
  return (options?.client ?? client).post<
    ResetForgotPasswordResponse,
    ResetForgotPasswordError,
    ThrowOnError
  >({
    ...options,
    url: "/auth/forgot-password",
  });
};

/**
 * Reset:Reset Password
 */
export const resetResetPassword = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<ResetResetPasswordData, ThrowOnError>,
) => {
  return (options?.client ?? client).post<
    ResetResetPasswordResponse,
    ResetResetPasswordError,
    ThrowOnError
  >({
    ...options,
    url: "/auth/reset-password",
  });
};

/**
 * Verify:Request-Token
 */
export const verifyRequestToken = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<VerifyRequestTokenData, ThrowOnError>,
) => {
  return (options?.client ?? client).post<
    VerifyRequestTokenResponse,
    VerifyRequestTokenError,
    ThrowOnError
  >({
    ...options,
    url: "/auth/request-verify-token",
  });
};

/**
 * Verify:Verify
 */
export const verifyVerify = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<VerifyVerifyData, ThrowOnError>,
) => {
  return (options?.client ?? client).post<
    VerifyVerifyResponse,
    VerifyVerifyError,
    ThrowOnError
  >({
    ...options,
    url: "/auth/verify",
  });
};

/**
 * Users:Current User
 */
export const usersCurrentUser = <ThrowOnError extends boolean = false>(
  options?: OptionsLegacyParser<unknown, ThrowOnError>,
) => {
  return (options?.client ?? client).get<
    UsersCurrentUserResponse,
    UsersCurrentUserError,
    ThrowOnError
  >({
    ...options,
    url: "/users/me",
  });
};

/**
 * Users:Patch Current User
 */
export const usersPatchCurrentUser = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<UsersPatchCurrentUserData, ThrowOnError>,
) => {
  return (options?.client ?? client).patch<
    UsersPatchCurrentUserResponse,
    UsersPatchCurrentUserError,
    ThrowOnError
  >({
    ...options,
    url: "/users/me",
  });
};

/**
 * Users:User
 */
export const usersUser = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<UsersUserData, ThrowOnError>,
) => {
  return (options?.client ?? client).get<
    UsersUserResponse,
    UsersUserError,
    ThrowOnError
  >({
    ...options,
    url: "/users/{id}",
  });
};

/**
 * Users:Patch User
 */
export const usersPatchUser = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<UsersPatchUserData, ThrowOnError>,
) => {
  return (options?.client ?? client).patch<
    UsersPatchUserResponse,
    UsersPatchUserError,
    ThrowOnError
  >({
    ...options,
    url: "/users/{id}",
  });
};

/**
 * Users:Delete User
 */
export const usersDeleteUser = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<UsersDeleteUserData, ThrowOnError>,
) => {
  return (options?.client ?? client).delete<
    UsersDeleteUserResponse,
    UsersDeleteUserError,
    ThrowOnError
  >({
    ...options,
    url: "/users/{id}",
  });
};

/**
 * Read Item
 */
export const readItem = <ThrowOnError extends boolean = false>(
  options?: OptionsLegacyParser<unknown, ThrowOnError>,
) => {
  return (options?.client ?? client).get<
    ReadItemResponse,
    ReadItemError,
    ThrowOnError
  >({
    ...options,
    url: "/items/",
  });
};

/**
 * Create Item
 */
export const createItem = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<CreateItemData, ThrowOnError>,
) => {
  return (options?.client ?? client).post<
    CreateItemResponse,
    CreateItemError,
    ThrowOnError
  >({
    ...options,
    url: "/items/",
  });
};

/**
 * Delete Item
 */
export const deleteItem = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<DeleteItemData, ThrowOnError>,
) => {
  return (options?.client ?? client).delete<
    DeleteItemResponse,
    DeleteItemError,
    ThrowOnError
  >({
    ...options,
    url: "/items/{item_id}",
  });
};

/**
 * Create Keyword
 * 创建新关键词
 */
export const createKeyword = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<CreateKeywordData, ThrowOnError>,
) => {
  return (options?.client ?? client).post<
    CreateKeywordResponse,
    CreateKeywordError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/keywords/",
  });
};

/**
 * List Keywords
 * 获取关键词列表
 */
export const listKeywords = <ThrowOnError extends boolean = false>(
  options?: OptionsLegacyParser<ListKeywordsData, ThrowOnError>,
) => {
  return (options?.client ?? client).get<
    ListKeywordsResponse,
    ListKeywordsError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/keywords/",
  });
};

/**
 * Get Keyword
 * 获取单个关键词详情
 */
export const getKeyword = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<GetKeywordData, ThrowOnError>,
) => {
  return (options?.client ?? client).get<
    GetKeywordResponse,
    GetKeywordError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/keywords/{keyword_id}",
  });
};

/**
 * Update Keyword
 * 更新关键词
 */
export const updateKeyword = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<UpdateKeywordData, ThrowOnError>,
) => {
  return (options?.client ?? client).put<
    UpdateKeywordResponse,
    UpdateKeywordError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/keywords/{keyword_id}",
  });
};

/**
 * Delete Keyword
 * 删除关键词
 */
export const deleteKeyword = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<DeleteKeywordData, ThrowOnError>,
) => {
  return (options?.client ?? client).delete<
    DeleteKeywordResponse,
    DeleteKeywordError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/keywords/{keyword_id}",
  });
};

/**
 * Analyze Keywords
 * 分析关键词并存储到数据库
 */
export const analyzeKeywords = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<AnalyzeKeywordsData, ThrowOnError>,
) => {
  return (options?.client ?? client).post<
    AnalyzeKeywordsResponse,
    AnalyzeKeywordsError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/keywords/analyze",
  });
};

/**
 * Calculate Potential Score
 * 重新计算关键词潜力分数
 */
export const calculatePotentialScore = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<CalculatePotentialScoreData, ThrowOnError>,
) => {
  return (options?.client ?? client).post<
    CalculatePotentialScoreResponse,
    CalculatePotentialScoreError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/keywords/{keyword_id}/calculate-score",
  });
};

/**
 * Approve Keyword
 * 批准关键词用于建站
 */
export const approveKeyword = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<ApproveKeywordData, ThrowOnError>,
) => {
  return (options?.client ?? client).post<
    ApproveKeywordResponse,
    ApproveKeywordError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/keywords/{keyword_id}/approve",
  });
};

/**
 * Reject Keyword
 * 拒绝关键词
 */
export const rejectKeyword = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<RejectKeywordData, ThrowOnError>,
) => {
  return (options?.client ?? client).post<
    RejectKeywordResponse,
    RejectKeywordError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/keywords/{keyword_id}/reject",
  });
};

/**
 * Create Seed Keyword
 * 创建种子关键词
 */
export const createSeedKeyword = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<CreateSeedKeywordData, ThrowOnError>,
) => {
  return (options?.client ?? client).post<
    CreateSeedKeywordResponse,
    CreateSeedKeywordError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/keywords/seeds",
  });
};

/**
 * List Seed Keywords
 * 获取种子关键词列表
 */
export const listSeedKeywords = <ThrowOnError extends boolean = false>(
  options?: OptionsLegacyParser<ListSeedKeywordsData, ThrowOnError>,
) => {
  return (options?.client ?? client).get<
    ListSeedKeywordsResponse,
    ListSeedKeywordsError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/keywords/seeds",
  });
};

/**
 * Expand Seed Keyword
 * 扩展种子关键词生成相关关键词
 */
export const expandSeedKeyword = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<ExpandSeedKeywordData, ThrowOnError>,
) => {
  return (options?.client ?? client).post<
    ExpandSeedKeywordResponse,
    ExpandSeedKeywordError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/keywords/seeds/{seed_id}/expand",
  });
};

/**
 * Create Website
 * 创建新网站
 */
export const createWebsite = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<CreateWebsiteData, ThrowOnError>,
) => {
  return (options?.client ?? client).post<
    CreateWebsiteResponse,
    CreateWebsiteError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/websites/",
  });
};

/**
 * List Websites
 * 获取网站列表
 */
export const listWebsites = <ThrowOnError extends boolean = false>(
  options?: OptionsLegacyParser<ListWebsitesData, ThrowOnError>,
) => {
  return (options?.client ?? client).get<
    ListWebsitesResponse,
    ListWebsitesError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/websites/",
  });
};

/**
 * Get Website
 * 获取单个网站详情
 */
export const getWebsite = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<GetWebsiteData, ThrowOnError>,
) => {
  return (options?.client ?? client).get<
    GetWebsiteResponse,
    GetWebsiteError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/websites/{website_id}",
  });
};

/**
 * Update Website
 * 更新网站
 */
export const updateWebsite = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<UpdateWebsiteData, ThrowOnError>,
) => {
  return (options?.client ?? client).put<
    UpdateWebsiteResponse,
    UpdateWebsiteError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/websites/{website_id}",
  });
};

/**
 * Delete Website
 * 删除网站
 */
export const deleteWebsite = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<DeleteWebsiteData, ThrowOnError>,
) => {
  return (options?.client ?? client).delete<
    DeleteWebsiteResponse,
    DeleteWebsiteError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/websites/{website_id}",
  });
};

/**
 * Regenerate Website
 * 重新生成网站
 */
export const regenerateWebsite = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<RegenerateWebsiteData, ThrowOnError>,
) => {
  return (options?.client ?? client).post<
    RegenerateWebsiteResponse,
    RegenerateWebsiteError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/websites/{website_id}/regenerate",
  });
};

/**
 * Deploy Website
 * 部署网站
 */
export const deployWebsite = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<DeployWebsiteData, ThrowOnError>,
) => {
  return (options?.client ?? client).post<
    DeployWebsiteResponse,
    DeployWebsiteError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/websites/{website_id}/deploy",
  });
};

/**
 * Get Website Tasks
 * 获取网站任务列表
 */
export const getWebsiteTasks = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<GetWebsiteTasksData, ThrowOnError>,
) => {
  return (options?.client ?? client).get<
    GetWebsiteTasksResponse,
    GetWebsiteTasksError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/websites/{website_id}/tasks",
  });
};

/**
 * Batch Generate Websites
 * 批量生成网站
 */
export const batchGenerateWebsites = <ThrowOnError extends boolean = false>(
  options: OptionsLegacyParser<BatchGenerateWebsitesData, ThrowOnError>,
) => {
  return (options?.client ?? client).post<
    BatchGenerateWebsitesResponse,
    BatchGenerateWebsitesError,
    ThrowOnError
  >({
    ...options,
    url: "/api/v1/websites/batch-generate",
  });
};
