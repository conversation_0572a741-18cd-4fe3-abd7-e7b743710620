site_name: Next.js FastAPI Template
site_description: Kickstart scalable apps with our Next.js FastAPI Template. Includes auth, type safety (Zod), hot reload, Docker, and Vercel-ready deployment.
site_url: https://vintasoftware.github.io/nextjs-fastapi-template/

repo_name: vintasoftware/nextjs-fastapi-template
repo_url: https://github.com/vintasoftware/nextjs-fastapi-template/
edit_uri: blob/main/docs/

copyright: From <a href="https://www.vintasoftware.com">Vinta Software</a> to the community with 💙

theme:
  name: material
  custom_dir: overrides
  logo: images/nav-logo.png
  favicon: images/github-favicon.png
  features:
    - navigation.footer
    - navigation.indexes
    - navigation.sections
    - navigation.tabs
    - navigation.top
    - navigation.tracking
    - search.highlight
    - search.share
    - search.suggest
    - toc.follow
  palette:
    # Palette toggle for automatic mode
    - media: "(prefers-color-scheme)"
      primary: custom
      toggle:
        icon: material/brightness-auto
        name: Switch to light mode

    # Palette toggle for light mode
    - media: "(prefers-color-scheme: light)"
      scheme: default
      primary: custom
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode

    # Palette toggle for dark mode
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      primary: custom
      toggle:
        icon: material/brightness-4
        name: Switch to system preference

markdown_extensions:
  - admonition
  - pymdownx.highlight:
      use_pygments: true
  - pymdownx.inlinehilite
  - pymdownx.superfences
  - pymdownx.snippets:
      check_paths: true
  - toc:
      permalink: true
  - attr_list
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg

nav:
  - Home: README.md
  - Get Started: get-started.md
  - Additional Settings: additional-settings.md
  - Technology Selection: technology-selection.md
  - Deployment: deployment.md
  - Changelog: CHANGELOG.md
  - Contributing: contributing.md
  - Support: support.md


plugins:
  - search

extra:
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/vintasoftware/nextjs-fastapi-template/
      name: Nextjs FastAPI Template GitHub
    - icon: fontawesome/brands/x-twitter
      link: https://x.com/vintasoftware
      name: Vinta Software X
    - icon: fontawesome/brands/linkedin
      link: https://linkedin.com/company/vintasoftware
      name: Vinta Software LinkedIn
  version:
    provider: mike
  analytics:
    provider: google
    property: GTM-M9GMGBHR

extra_css:
  - stylesheets/extra.css
