# Local database
DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/mydatabase

# Test database
TEST_DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5433/testdatabase

# Secret keys
ACCESS_SECRET_KEY=your_access_secret_key
RESET_PASSWORD_SECRET_KEY=your_reset_password_secret_key
VERIFICATION_SECRET_KEY=your_verification_secret_key

# OpenAPI genrated file output path
OPENAPI_OUTPUT_FILE=../nextjs-frontend/openapi.json

# Localhost Email configuration
MAIL_USERNAME=test
MAIL_PASSWORD=test
MAIL_FROM=<EMAIL>
MAIL_PORT=1025
MAIL_SERVER=localhost
MAIL_STARTTLS=False
MAIL_SSL_TLS=False
USE_CREDENTIALS=False

# Frontend (NextJS)
FRONTEND_URL=http://localhost:3000

# CORS (Allow all origins for development; adjust this to restrict origins in production deployments)
CORS_ORIGINS=["*"]

# OPENAPI (Uncomment the line below to disable the /docs and openapi.json urls)
# OPENAPI_URL=""