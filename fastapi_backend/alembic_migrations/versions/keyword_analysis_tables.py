"""Add keyword analysis tables

Revision ID: keyword_analysis_001
Revises: b389592974f8
Create Date: 2025-06-27 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'keyword_analysis_001'
down_revision = 'b389592974f8'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create keywords table
    op.create_table('keywords',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('keyword', sa.String(length=255), nullable=False),
        sa.Column('language', sa.String(length=10), nullable=False),
        sa.Column('country', sa.String(length=10), nullable=False),
        sa.Column('search_volume', sa.Integer(), nullable=True),
        sa.Column('keyword_difficulty', sa.Float(), nullable=True),
        sa.Column('cpc', sa.Float(), nullable=True),
        sa.Column('competition', sa.Float(), nullable=True),
        sa.Column('search_intent', sa.String(length=50), nullable=True),
        sa.Column('zero_click_rate', sa.Float(), nullable=True),
        sa.Column('trend_score', sa.Float(), nullable=True),
        sa.Column('trend_data', sa.JSON(), nullable=True),
        sa.Column('serp_quality_score', sa.Float(), nullable=True),
        sa.Column('serp_analysis', sa.JSON(), nullable=True),
        sa.Column('tool_complexity', sa.Integer(), nullable=True),
        sa.Column('tool_category', sa.String(length=100), nullable=True),
        sa.Column('potential_score', sa.Float(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=False),
        sa.Column('is_approved', sa.Boolean(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('last_checked_at', sa.DateTime(), nullable=True),
        sa.Column('next_check_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_keywords_keyword'), 'keywords', ['keyword'], unique=False)

    # Create websites table
    op.create_table('websites',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('keyword_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('domain', sa.String(length=255), nullable=True),
        sa.Column('subdomain', sa.String(length=255), nullable=True),
        sa.Column('title', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('content_data', sa.JSON(), nullable=True),
        sa.Column('seo_metadata', sa.JSON(), nullable=True),
        sa.Column('template_id', sa.String(length=100), nullable=True),
        sa.Column('github_repo', sa.String(length=255), nullable=True),
        sa.Column('deployment_url', sa.String(length=255), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=False),
        sa.Column('build_log', sa.Text(), nullable=True),
        sa.Column('lighthouse_score', sa.JSON(), nullable=True),
        sa.Column('page_speed', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('deployed_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['keyword_id'], ['keywords.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create keyword_tasks table
    op.create_table('keyword_tasks',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('keyword_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('task_type', sa.String(length=50), nullable=False),
        sa.Column('task_status', sa.String(length=20), nullable=False),
        sa.Column('celery_task_id', sa.String(length=255), nullable=True),
        sa.Column('task_config', sa.JSON(), nullable=True),
        sa.Column('result_data', sa.JSON(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('started_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['keyword_id'], ['keywords.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create website_tasks table
    op.create_table('website_tasks',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('website_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('task_type', sa.String(length=50), nullable=False),
        sa.Column('task_status', sa.String(length=20), nullable=False),
        sa.Column('celery_task_id', sa.String(length=255), nullable=True),
        sa.Column('task_config', sa.JSON(), nullable=True),
        sa.Column('result_data', sa.JSON(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('started_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['website_id'], ['websites.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create seed_keywords table
    op.create_table('seed_keywords',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('root_word', sa.String(length=100), nullable=False),
        sa.Column('category', sa.String(length=100), nullable=True),
        sa.Column('priority', sa.Integer(), nullable=False),
        sa.Column('modifiers', sa.JSON(), nullable=True),
        sa.Column('language', sa.String(length=10), nullable=False),
        sa.Column('country', sa.String(length=10), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('last_expanded_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )


def downgrade() -> None:
    op.drop_table('seed_keywords')
    op.drop_table('website_tasks')
    op.drop_table('keyword_tasks')
    op.drop_table('websites')
    op.drop_index(op.f('ix_keywords_keyword'), table_name='keywords')
    op.drop_table('keywords')