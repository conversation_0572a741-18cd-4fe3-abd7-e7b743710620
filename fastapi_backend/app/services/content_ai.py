"""AI内容生成服务 - 集成GPT-4 API进行智能内容生成"""

import openai
import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import logging
import asyncio
from jinja2 import Template

logger = logging.getLogger(__name__)


class ContentType(Enum):
    """内容类型枚举"""
    WEBSITE_COPY = "website_copy"
    TOOL_DESCRIPTION = "tool_description"
    SEO_CONTENT = "seo_content"
    FAQ = "faq"
    FEATURES = "features"
    HOW_TO_USE = "how_to_use"
    BLOG_POST = "blog_post"
    META_TAGS = "meta_tags"


@dataclass
class ContentRequest:
    """内容生成请求"""
    content_type: ContentType
    keyword: str
    tool_type: str
    target_audience: str = "general users"
    tone: str = "professional"  # professional, friendly, technical, casual
    language: str = "en"
    additional_context: Optional[Dict[str, Any]] = None


@dataclass
class GeneratedContent:
    """生成的内容"""
    content_type: ContentType
    title: Optional[str] = None
    content: str = ""
    meta_data: Optional[Dict[str, Any]] = None
    suggestions: Optional[List[str]] = None


class PromptTemplates:
    """提示词模板"""
    
    @staticmethod
    def get_website_copy_prompt() -> str:
        return """
        Create compelling website copy for a {{ tool_type }} tool focused on "{{ keyword }}".
        
        Target Audience: {{ target_audience }}
        Tone: {{ tone }}
        Language: {{ language }}
        
        Generate the following sections:
        1. Hero headline (compelling, benefit-focused)
        2. Subheadline (clarifying the value proposition)
        3. Primary call-to-action text
        4. Benefits summary (3-4 key benefits)
        5. Trust indicators text
        
        Requirements:
        - Focus on user benefits, not just features
        - Use action-oriented language
        - Include relevant keywords naturally
        - Make it conversion-focused
        - Ensure clarity and readability
        
        {% if additional_context %}
        Additional Context: {{ additional_context }}
        {% endif %}
        
        Return the response in JSON format with keys: headline, subheadline, cta_text, benefits, trust_indicators
        """
    
    @staticmethod
    def get_seo_content_prompt() -> str:
        return """
        Create SEO-optimized content for a {{ tool_type }} focused on "{{ keyword }}".
        
        Target Audience: {{ target_audience }}
        Language: {{ language }}
        
        Generate:
        1. SEO-optimized title (50-60 characters)
        2. Meta description (150-160 characters)
        3. H1 heading
        4. 3-5 H2 subheadings
        5. 5-10 relevant keywords for meta keywords tag
        6. Schema.org structured data suggestions
        
        Requirements:
        - Include target keyword in title and meta description
        - Use semantic keywords and variations
        - Focus on search intent satisfaction
        - Ensure readability and user experience
        
        {% if additional_context %}
        Additional Context: {{ additional_context }}
        {% endif %}
        
        Return as JSON with keys: title, meta_description, h1, h2_headings, keywords, schema_suggestions
        """
    
    @staticmethod
    def get_features_prompt() -> str:
        return """
        Create a comprehensive list of features for a {{ tool_type }} tool that handles "{{ keyword }}".
        
        Target Audience: {{ target_audience }}
        Tone: {{ tone }}
        Language: {{ language }}
        
        Generate 6-8 features, each with:
        1. Feature name (concise, benefit-focused)
        2. Description (1-2 sentences explaining the benefit)
        3. Icon suggestion (simple description for icon choice)
        
        Requirements:
        - Focus on user value and benefits
        - Make features specific and tangible
        - Use clear, jargon-free language
        - Ensure features are realistic for a {{ tool_type }}
        
        {% if additional_context %}
        Additional Context: {{ additional_context }}
        {% endif %}
        
        Return as JSON array with objects containing: name, description, icon_suggestion
        """
    
    @staticmethod
    def get_faq_prompt() -> str:
        return """
        Create a comprehensive FAQ section for a {{ tool_type }} tool focused on "{{ keyword }}".
        
        Target Audience: {{ target_audience }}
        Language: {{ language }}
        
        Generate 8-12 frequently asked questions and answers covering:
        1. Basic usage questions
        2. Technical specifications
        3. Security and privacy concerns
        4. Pricing and access questions
        5. Troubleshooting common issues
        6. Feature-specific questions
        
        Requirements:
        - Use natural, conversational language
        - Provide clear, helpful answers
        - Address common user concerns
        - Include relevant keywords naturally
        - Keep answers concise but informative
        
        {% if additional_context %}
        Additional Context: {{ additional_context }}
        {% endif %}
        
        Return as JSON array with objects containing: question, answer
        """
    
    @staticmethod
    def get_how_to_use_prompt() -> str:
        return """
        Create clear, step-by-step instructions for using a {{ tool_type }} tool for "{{ keyword }}".
        
        Target Audience: {{ target_audience }}
        Tone: {{ tone }}
        Language: {{ language }}
        
        Generate:
        1. Brief introduction paragraph
        2. Step-by-step instructions (4-8 steps)
        3. Tips for best results
        4. Common mistakes to avoid
        5. Advanced usage tips (optional)
        
        Requirements:
        - Use clear, action-oriented language
        - Make each step specific and actionable
        - Consider user experience and ease of use
        - Include helpful tips and warnings
        
        {% if additional_context %}
        Additional Context: {{ additional_context }}
        {% endif %}
        
        Return as JSON with keys: introduction, steps, tips, common_mistakes, advanced_tips
        """


class ContentAI:
    """AI内容生成器"""
    
    def __init__(self, api_key: str, model: str = "gpt-4"):
        self.client = openai.AsyncOpenAI(api_key=api_key)
        self.model = model
        self.prompt_templates = PromptTemplates()
    
    async def generate_content(self, request: ContentRequest) -> GeneratedContent:
        """生成内容"""
        
        try:
            # 获取对应的提示词模板
            prompt_template = self._get_prompt_template(request.content_type)
            
            # 渲染提示词
            rendered_prompt = self._render_prompt(prompt_template, request)
            
            # 调用OpenAI API
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert content creator and copywriter specializing in creating high-converting, SEO-optimized content for online tools and web applications."
                    },
                    {
                        "role": "user",
                        "content": rendered_prompt
                    }
                ],
                temperature=0.7,
                max_tokens=2000
            )
            
            # 解析响应
            content_text = response.choices[0].message.content
            
            # 尝试解析JSON响应
            try:
                content_data = json.loads(content_text)
                return self._parse_content_response(request.content_type, content_data)
            except json.JSONDecodeError:
                # 如果不是JSON格式，返回纯文本
                return GeneratedContent(
                    content_type=request.content_type,
                    content=content_text
                )
        
        except Exception as e:
            logger.error(f"Error generating content: {str(e)}")
            return GeneratedContent(
                content_type=request.content_type,
                content=f"Error generating content: {str(e)}"
            )
    
    async def generate_multiple_content(self, requests: List[ContentRequest]) -> List[GeneratedContent]:
        """批量生成内容"""
        
        # 限制并发数量以避免API限制
        semaphore = asyncio.Semaphore(3)
        
        async def generate_with_semaphore(request):
            async with semaphore:
                return await self.generate_content(request)
        
        tasks = [generate_with_semaphore(req) for req in requests]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤异常结果
        valid_results = []
        for result in results:
            if isinstance(result, GeneratedContent):
                valid_results.append(result)
            else:
                logger.error(f"Error in batch generation: {result}")
                valid_results.append(GeneratedContent(
                    content_type=ContentType.WEBSITE_COPY,
                    content="Generation failed"
                ))
        
        return valid_results
    
    def _get_prompt_template(self, content_type: ContentType) -> str:
        """获取提示词模板"""
        
        template_map = {
            ContentType.WEBSITE_COPY: self.prompt_templates.get_website_copy_prompt(),
            ContentType.SEO_CONTENT: self.prompt_templates.get_seo_content_prompt(),
            ContentType.FEATURES: self.prompt_templates.get_features_prompt(),
            ContentType.FAQ: self.prompt_templates.get_faq_prompt(),
            ContentType.HOW_TO_USE: self.prompt_templates.get_how_to_use_prompt(),
        }
        
        return template_map.get(content_type, self.prompt_templates.get_website_copy_prompt())
    
    def _render_prompt(self, template_str: str, request: ContentRequest) -> str:
        """渲染提示词模板"""
        
        template = Template(template_str)
        return template.render(
            keyword=request.keyword,
            tool_type=request.tool_type,
            target_audience=request.target_audience,
            tone=request.tone,
            language=request.language,
            additional_context=request.additional_context or {}
        )
    
    def _parse_content_response(self, content_type: ContentType, data: Dict[str, Any]) -> GeneratedContent:
        """解析内容响应"""
        
        if content_type == ContentType.WEBSITE_COPY:
            return GeneratedContent(
                content_type=content_type,
                title=data.get("headline"),
                content=json.dumps(data, indent=2),
                meta_data={
                    "headline": data.get("headline"),
                    "subheadline": data.get("subheadline"),
                    "cta_text": data.get("cta_text"),
                    "benefits": data.get("benefits"),
                    "trust_indicators": data.get("trust_indicators")
                }
            )
        
        elif content_type == ContentType.SEO_CONTENT:
            return GeneratedContent(
                content_type=content_type,
                title=data.get("title"),
                content=data.get("meta_description", ""),
                meta_data={
                    "title": data.get("title"),
                    "meta_description": data.get("meta_description"),
                    "h1": data.get("h1"),
                    "h2_headings": data.get("h2_headings"),
                    "keywords": data.get("keywords"),
                    "schema_suggestions": data.get("schema_suggestions")
                }
            )
        
        elif content_type == ContentType.FEATURES:
            return GeneratedContent(
                content_type=content_type,
                content=json.dumps(data, indent=2),
                meta_data={"features": data}
            )
        
        elif content_type == ContentType.FAQ:
            return GeneratedContent(
                content_type=content_type,
                content=json.dumps(data, indent=2),
                meta_data={"faq": data}
            )
        
        elif content_type == ContentType.HOW_TO_USE:
            return GeneratedContent(
                content_type=content_type,
                content=json.dumps(data, indent=2),
                meta_data={
                    "introduction": data.get("introduction"),
                    "steps": data.get("steps"),
                    "tips": data.get("tips"),
                    "common_mistakes": data.get("common_mistakes"),
                    "advanced_tips": data.get("advanced_tips")
                }
            )
        
        else:
            return GeneratedContent(
                content_type=content_type,
                content=json.dumps(data, indent=2),
                meta_data=data
            )


class WebsiteContentGenerator:
    """网站内容生成器 - 专门为网站生成完整内容"""
    
    def __init__(self, content_ai: ContentAI):
        self.content_ai = content_ai
    
    async def generate_complete_website_content(
        self,
        keyword: str,
        tool_type: str,
        target_audience: str = "general users",
        tone: str = "professional",
        language: str = "en"
    ) -> Dict[str, Any]:
        """生成完整的网站内容"""
        
        # 定义需要生成的内容类型
        content_requests = [
            ContentRequest(
                content_type=ContentType.WEBSITE_COPY,
                keyword=keyword,
                tool_type=tool_type,
                target_audience=target_audience,
                tone=tone,
                language=language
            ),
            ContentRequest(
                content_type=ContentType.SEO_CONTENT,
                keyword=keyword,
                tool_type=tool_type,
                target_audience=target_audience,
                tone=tone,
                language=language
            ),
            ContentRequest(
                content_type=ContentType.FEATURES,
                keyword=keyword,
                tool_type=tool_type,
                target_audience=target_audience,
                tone=tone,
                language=language
            ),
            ContentRequest(
                content_type=ContentType.FAQ,
                keyword=keyword,
                tool_type=tool_type,
                target_audience=target_audience,
                tone=tone,
                language=language
            ),
            ContentRequest(
                content_type=ContentType.HOW_TO_USE,
                keyword=keyword,
                tool_type=tool_type,
                target_audience=target_audience,
                tone=tone,
                language=language
            )
        ]
        
        # 批量生成内容
        generated_contents = await self.content_ai.generate_multiple_content(content_requests)
        
        # 组织内容结构
        website_content = {
            "keyword": keyword,
            "tool_type": tool_type,
            "target_audience": target_audience,
            "tone": tone,
            "language": language,
            "generated_at": "2024-01-01T00:00:00Z",  # 实际应该使用当前时间
            "content": {}
        }
        
        # 处理每种类型的内容
        for content in generated_contents:
            content_key = content.content_type.value
            
            if content.meta_data:
                website_content["content"][content_key] = content.meta_data
            else:
                website_content["content"][content_key] = {
                    "title": content.title,
                    "content": content.content
                }
        
        return website_content
    
    def format_for_website_generator(self, ai_content: Dict[str, Any]) -> Dict[str, Any]:
        """将AI生成的内容格式化为网站生成器可用的格式"""
        
        formatted_content = {
            "title": "",
            "description": "",
            "meta_keywords": [],
            "hero_section": {},
            "features": [],
            "how_to_use": [],
            "faq": [],
            "about": "",
            "footer": {}
        }
        
        content = ai_content.get("content", {})
        
        # SEO内容
        seo_content = content.get("seo_content", {})
        if seo_content:
            formatted_content["title"] = seo_content.get("title", "")
            formatted_content["description"] = seo_content.get("meta_description", "")
            formatted_content["meta_keywords"] = seo_content.get("keywords", [])
        
        # 网站文案
        website_copy = content.get("website_copy", {})
        if website_copy:
            formatted_content["hero_section"] = {
                "headline": website_copy.get("headline", ""),
                "subheadline": website_copy.get("subheadline", ""),
                "cta_text": website_copy.get("cta_text", "Get Started"),
                "features_text": " | ".join(website_copy.get("trust_indicators", []))
            }
        
        # 功能特性
        features_content = content.get("features", {})
        if features_content and "features" in features_content:
            formatted_content["features"] = features_content["features"]
        
        # 使用说明
        how_to_content = content.get("how_to_use", {})
        if how_to_content and "steps" in how_to_content:
            formatted_content["how_to_use"] = how_to_content["steps"]
        
        # FAQ
        faq_content = content.get("faq", {})
        if faq_content and "faq" in faq_content:
            formatted_content["faq"] = faq_content["faq"]
        
        # 关于section
        if how_to_content and "introduction" in how_to_content:
            formatted_content["about"] = how_to_content["introduction"]
        
        # 页脚
        formatted_content["footer"] = {
            "copyright": f"© 2024 {ai_content.get('keyword', 'Tool').title()} Tool. All rights reserved.",
            "privacy_policy": "Privacy Policy",
            "terms_of_service": "Terms of Service",
            "contact": "Contact Us"
        }
        
        return formatted_content


class ContentOptimizer:
    """内容优化器"""
    
    def __init__(self, content_ai: ContentAI):
        self.content_ai = content_ai
    
    async def optimize_for_keyword(self, content: str, keyword: str, density_target: float = 0.02) -> str:
        """优化内容的关键词密度"""
        
        # 这里可以实现关键词密度优化逻辑
        # 简化版本：确保关键词在内容中出现合适的次数
        
        word_count = len(content.split())
        current_keyword_count = content.lower().count(keyword.lower())
        target_count = int(word_count * density_target)
        
        if current_keyword_count < target_count:
            # 需要增加关键词出现次数
            optimization_prompt = f"""
            Optimize this content to include the keyword "{keyword}" {target_count - current_keyword_count} more times naturally:
            
            {content}
            
            Requirements:
            - Add the keyword naturally without keyword stuffing
            - Maintain readability and flow
            - Use semantic variations where appropriate
            """
            
            request = ContentRequest(
                content_type=ContentType.WEBSITE_COPY,
                keyword=keyword,
                tool_type="content optimization",
                additional_context={"optimization_prompt": optimization_prompt}
            )
            
            optimized = await self.content_ai.generate_content(request)
            return optimized.content
        
        return content
    
    def analyze_content_quality(self, content: str) -> Dict[str, Any]:
        """分析内容质量"""
        
        word_count = len(content.split())
        sentence_count = content.count('.') + content.count('!') + content.count('?')
        paragraph_count = content.count('\n\n') + 1
        
        # 计算可读性指标
        avg_words_per_sentence = word_count / max(sentence_count, 1)
        avg_sentences_per_paragraph = sentence_count / max(paragraph_count, 1)
        
        # 简单的质量评分
        quality_score = 0.0
        
        # 字数评分
        if 300 <= word_count <= 2000:
            quality_score += 0.3
        elif word_count > 100:
            quality_score += 0.2
        
        # 句子长度评分
        if 10 <= avg_words_per_sentence <= 20:
            quality_score += 0.3
        elif avg_words_per_sentence <= 25:
            quality_score += 0.2
        
        # 段落结构评分
        if 2 <= avg_sentences_per_paragraph <= 5:
            quality_score += 0.2
        
        # 内容结构评分（是否有标题、列表等）
        if any(marker in content for marker in ['#', '*', '-', '1.', '2.']):
            quality_score += 0.2
        
        return {
            "quality_score": min(quality_score, 1.0),
            "word_count": word_count,
            "sentence_count": sentence_count,
            "paragraph_count": paragraph_count,
            "avg_words_per_sentence": avg_words_per_sentence,
            "avg_sentences_per_paragraph": avg_sentences_per_paragraph,
            "recommendations": self._get_content_recommendations(quality_score, word_count, avg_words_per_sentence)
        }
    
    def _get_content_recommendations(self, quality_score: float, word_count: int, avg_words_per_sentence: float) -> List[str]:
        """获取内容改进建议"""
        
        recommendations = []
        
        if quality_score < 0.6:
            recommendations.append("内容质量需要改进")
        
        if word_count < 300:
            recommendations.append("内容长度偏短，建议增加更多有价值的信息")
        elif word_count > 2000:
            recommendations.append("内容可能过长，考虑分段或精简")
        
        if avg_words_per_sentence > 25:
            recommendations.append("句子过长，建议分解为更短的句子以提高可读性")
        elif avg_words_per_sentence < 8:
            recommendations.append("句子过短，可以适当增加描述性内容")
        
        if not recommendations:
            recommendations.append("内容质量良好")
        
        return recommendations