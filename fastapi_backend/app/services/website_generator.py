"""网站生成服务 - 自动生成工具网站"""

import os
import json
import shutil
from typing import Dict, List, Optional, Any
from pathlib import Path
from jinja2 import Environment, FileSystemLoader, Template
from dataclasses import dataclass
import logging
from uuid import uuid4

logger = logging.getLogger(__name__)


@dataclass
class WebsiteConfig:
    """网站配置"""
    keyword: str
    title: str
    description: str
    tool_type: str
    template_id: str
    domain: Optional[str] = None
    language: str = "en"
    
    # SEO配置
    meta_keywords: List[str] = None
    og_image: Optional[str] = None
    
    # 内容配置
    features: List[str] = None
    faq: List[Dict[str, str]] = None
    how_to_use: List[str] = None
    
    # 工具配置
    tool_config: Dict[str, Any] = None


@dataclass 
class TemplateInfo:
    """模板信息"""
    id: str
    name: str
    description: str
    tool_types: List[str]
    features: List[str]
    tech_stack: List[str]
    preview_url: Optional[str] = None


class TemplateManager:
    """模板管理器"""
    
    def __init__(self, templates_dir: str = "templates"):
        self.templates_dir = Path(templates_dir)
        self.available_templates = self._load_available_templates()
    
    def _load_available_templates(self) -> List[TemplateInfo]:
        """加载可用模板"""
        templates = [
            TemplateInfo(
                id="converter-basic",
                name="基础转换器模板",
                description="适用于文件格式转换、单位转换等简单转换工具",
                tool_types=["converter", "transformer"],
                features=["文件上传", "实时转换", "批量处理", "下载结果"],
                tech_stack=["Next.js", "React", "TailwindCSS", "TypeScript"]
            ),
            TemplateInfo(
                id="generator-standard",
                name="标准生成器模板", 
                description="适用于代码生成、内容生成等工具",
                tool_types=["generator", "maker", "creator"],
                features=["参数配置", "实时预览", "代码高亮", "一键复制"],
                tech_stack=["Next.js", "React", "TailwindCSS", "Monaco Editor"]
            ),
            TemplateInfo(
                id="calculator-advanced",
                name="高级计算器模板",
                description="适用于复杂计算、统计分析等工具",
                tool_types=["calculator", "analyzer"],
                features=["公式输入", "图表展示", "历史记录", "导出报告"],
                tech_stack=["Next.js", "React", "Chart.js", "MathJax"]
            ),
            TemplateInfo(
                id="editor-pro",
                name="专业编辑器模板",
                description="适用于文本编辑、代码编辑等工具",
                tool_types=["editor", "formatter"],
                features=["语法高亮", "实时预览", "插件系统", "协作编辑"],
                tech_stack=["Next.js", "Monaco Editor", "Socket.io"]
            ),
            TemplateInfo(
                id="universal-tool",
                name="通用工具模板",
                description="适用于各种类型的在线工具",
                tool_types=["tool", "utility", "helper"],
                features=["响应式设计", "PWA支持", "SEO优化", "多语言"],
                tech_stack=["Next.js", "React", "TailwindCSS", "i18next"]
            )
        ]
        return templates
    
    def get_template_by_id(self, template_id: str) -> Optional[TemplateInfo]:
        """根据ID获取模板"""
        return next((t for t in self.available_templates if t.id == template_id), None)
    
    def suggest_template(self, tool_type: str, keyword: str) -> TemplateInfo:
        """建议最适合的模板"""
        
        # 基于工具类型匹配
        for template in self.available_templates:
            if tool_type.lower() in template.tool_types:
                return template
        
        # 基于关键词匹配
        keyword_lower = keyword.lower()
        if any(word in keyword_lower for word in ["convert", "transform"]):
            return self.get_template_by_id("converter-basic")
        elif any(word in keyword_lower for word in ["generate", "create", "make"]):
            return self.get_template_by_id("generator-standard")
        elif any(word in keyword_lower for word in ["calculate", "compute"]):
            return self.get_template_by_id("calculator-advanced")
        elif any(word in keyword_lower for word in ["edit", "format"]):
            return self.get_template_by_id("editor-pro")
        
        # 默认返回通用模板
        return self.get_template_by_id("universal-tool")


class ContentGenerator:
    """内容生成器"""
    
    def __init__(self):
        self.jinja_env = Environment(
            loader=FileSystemLoader("templates/content"),
            autoescape=True
        )
    
    def generate_website_content(self, config: WebsiteConfig) -> Dict[str, str]:
        """生成网站内容"""
        
        content = {
            "title": self._generate_title(config),
            "description": self._generate_description(config),
            "meta_keywords": self._generate_meta_keywords(config),
            "hero_section": self._generate_hero_section(config),
            "features": self._generate_features(config),
            "how_to_use": self._generate_how_to_use(config),
            "faq": self._generate_faq(config),
            "about": self._generate_about_section(config),
            "footer": self._generate_footer(config)
        }
        
        return content
    
    def _generate_title(self, config: WebsiteConfig) -> str:
        """生成页面标题"""
        if config.title:
            return config.title
        
        # 基于关键词生成标题
        keyword = config.keyword.title()
        if "converter" in config.tool_type.lower():
            return f"Free Online {keyword} - Convert Files Instantly"
        elif "generator" in config.tool_type.lower():
            return f"{keyword} Generator - Create {keyword} Online"
        elif "calculator" in config.tool_type.lower():
            return f"{keyword} Calculator - Accurate Online Calculations"
        else:
            return f"{keyword} Tool - Free Online {config.tool_type.title()}"
    
    def _generate_description(self, config: WebsiteConfig) -> str:
        """生成页面描述"""
        if config.description:
            return config.description
        
        keyword = config.keyword.lower()
        tool_type = config.tool_type.lower()
        
        templates = {
            "converter": f"Convert {keyword} files online for free. Fast, secure, and easy-to-use {keyword} converter with batch processing support.",
            "generator": f"Generate {keyword} online with our free tool. Professional {keyword} generator with customizable options and instant results.",
            "calculator": f"Calculate {keyword} online with our accurate calculator. Free {keyword} calculation tool with detailed results and explanations.",
            "editor": f"Edit {keyword} online with our powerful editor. Free {keyword} editor with syntax highlighting and real-time preview.",
            "tool": f"Free online {keyword} tool. Professional {keyword} utility with advanced features and instant results."
        }
        
        return templates.get(tool_type, templates["tool"])
    
    def _generate_meta_keywords(self, config: WebsiteConfig) -> List[str]:
        """生成Meta关键词"""
        if config.meta_keywords:
            return config.meta_keywords
        
        base_keywords = [
            config.keyword,
            f"{config.keyword} online",
            f"free {config.keyword}",
            f"{config.keyword} tool",
            config.tool_type,
            f"online {config.tool_type}"
        ]
        
        return base_keywords
    
    def _generate_hero_section(self, config: WebsiteConfig) -> Dict[str, str]:
        """生成首页Hero区域内容"""
        keyword = config.keyword.title()
        tool_type = config.tool_type.title()
        
        return {
            "headline": f"Free Online {keyword} {tool_type}",
            "subheadline": f"Professional {keyword.lower()} {config.tool_type.lower()} with instant results",
            "cta_text": f"Start Using {tool_type}",
            "features_text": "✓ Free to use ✓ No registration required ✓ Secure and private"
        }
    
    def _generate_features(self, config: WebsiteConfig) -> List[Dict[str, str]]:
        """生成功能特性"""
        if config.features:
            return [{"title": f, "description": f"Advanced {f.lower()} functionality"} for f in config.features]
        
        # 基于工具类型生成默认特性
        feature_templates = {
            "converter": [
                {"title": "Fast Conversion", "description": "Convert files in seconds with our optimized algorithms"},
                {"title": "Batch Processing", "description": "Convert multiple files at once to save time"},
                {"title": "High Quality", "description": "Maintain original quality during conversion"},
                {"title": "Secure & Private", "description": "Files are processed locally and deleted automatically"}
            ],
            "generator": [
                {"title": "Customizable Output", "description": "Tailor the generated content to your specific needs"},
                {"title": "Multiple Formats", "description": "Support for various output formats and styles"},
                {"title": "Real-time Preview", "description": "See changes instantly as you modify parameters"},
                {"title": "Export Options", "description": "Download or copy results in multiple formats"}
            ],
            "calculator": [
                {"title": "Accurate Results", "description": "Precise calculations with detailed explanations"},
                {"title": "Step-by-step", "description": "See the calculation process broken down"},
                {"title": "History Tracking", "description": "Keep track of previous calculations"},
                {"title": "Export Results", "description": "Save or share your calculation results"}
            ]
        }
        
        return feature_templates.get(config.tool_type.lower(), feature_templates["converter"])
    
    def _generate_how_to_use(self, config: WebsiteConfig) -> List[str]:
        """生成使用说明"""
        if config.how_to_use:
            return config.how_to_use
        
        # 基于工具类型生成默认使用说明
        how_to_templates = {
            "converter": [
                f"Upload your {config.keyword} file using the file selector",
                "Choose your desired output format from the dropdown menu",
                "Click the 'Convert' button to start the conversion process",
                "Download your converted file when the process is complete"
            ],
            "generator": [
                "Configure the generation parameters using the form",
                f"Preview your {config.keyword} in real-time",
                "Adjust settings until you're satisfied with the result",
                "Copy or download your generated content"
            ],
            "calculator": [
                f"Enter your {config.keyword} values in the input fields",
                "Select calculation method if multiple options are available",
                "Click 'Calculate' to get your results",
                "View detailed explanations and export if needed"
            ]
        }
        
        return how_to_templates.get(config.tool_type.lower(), how_to_templates["converter"])
    
    def _generate_faq(self, config: WebsiteConfig) -> List[Dict[str, str]]:
        """生成FAQ"""
        if config.faq:
            return config.faq
        
        keyword = config.keyword.lower()
        tool_type = config.tool_type.lower()
        
        base_faq = [
            {
                "question": f"Is this {keyword} {tool_type} free to use?",
                "answer": f"Yes, our {keyword} {tool_type} is completely free to use with no hidden costs or registration requirements."
            },
            {
                "question": "Is my data secure?",
                "answer": "Absolutely. All processing is done securely and your files are automatically deleted after processing."
            },
            {
                "question": f"What formats does the {keyword} {tool_type} support?",
                "answer": f"Our {tool_type} supports all major {keyword} formats. See the format list in the tool interface for details."
            },
            {
                "question": "Do I need to install any software?",
                "answer": f"No, this is a web-based {tool_type} that works directly in your browser. No downloads or installations required."
            }
        ]
        
        return base_faq
    
    def _generate_about_section(self, config: WebsiteConfig) -> str:
        """生成关于section"""
        keyword = config.keyword.lower()
        tool_type = config.tool_type.lower()
        
        return f"""
        Our {keyword} {tool_type} is designed to provide fast, reliable, and secure {keyword} processing 
        directly in your web browser. Whether you're a professional or just need quick {keyword} processing, 
        our tool offers the features and performance you need.
        
        Built with modern web technologies, our {tool_type} ensures optimal performance and user experience 
        across all devices and browsers.
        """
    
    def _generate_footer(self, config: WebsiteConfig) -> Dict[str, str]:
        """生成页脚内容"""
        return {
            "copyright": f"© 2024 {config.keyword.title()} Tool. All rights reserved.",
            "privacy_policy": "Privacy Policy",
            "terms_of_service": "Terms of Service",
            "contact": "Contact Us"
        }


class WebsiteGenerator:
    """网站生成器主类"""
    
    def __init__(self, output_dir: str = "generated_sites"):
        self.output_dir = Path(output_dir)
        self.template_manager = TemplateManager()
        self.content_generator = ContentGenerator()
        
        # 确保输出目录存在
        self.output_dir.mkdir(exist_ok=True)
    
    def generate_website(self, config: WebsiteConfig) -> Dict[str, Any]:
        """生成完整网站"""
        
        try:
            # 1. 选择模板
            if not config.template_id:
                template = self.template_manager.suggest_template(config.tool_type, config.keyword)
                config.template_id = template.id
            else:
                template = self.template_manager.get_template_by_id(config.template_id)
            
            if not template:
                raise ValueError(f"Template {config.template_id} not found")
            
            # 2. 生成网站内容
            content = self.content_generator.generate_website_content(config)
            
            # 3. 创建项目目录
            project_id = str(uuid4())[:8]
            project_dir = self.output_dir / f"{config.keyword.replace(' ', '-').lower()}-{project_id}"
            project_dir.mkdir(exist_ok=True)
            
            # 4. 复制模板文件
            template_path = Path("templates") / "nextjs" / template.id
            if template_path.exists():
                shutil.copytree(template_path, project_dir, dirs_exist_ok=True)
            else:
                # 如果模板不存在，使用基础模板
                self._create_basic_nextjs_template(project_dir, config, content)
            
            # 5. 生成配置文件
            self._generate_config_files(project_dir, config, content)
            
            # 6. 生成组件文件
            self._generate_component_files(project_dir, config, content)
            
            # 7. 生成页面文件
            self._generate_page_files(project_dir, config, content)
            
            # 8. 更新package.json
            self._update_package_json(project_dir, config)
            
            return {
                "success": True,
                "project_id": project_id,
                "project_path": str(project_dir),
                "template_used": template.id,
                "files_generated": self._get_generated_files_list(project_dir)
            }
            
        except Exception as e:
            logger.error(f"Error generating website for {config.keyword}: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _create_basic_nextjs_template(self, project_dir: Path, config: WebsiteConfig, content: Dict[str, str]):
        """创建基础Next.js模板"""
        
        # 创建基础目录结构
        dirs = [
            "src/app",
            "src/components",
            "src/lib", 
            "public",
            "styles"
        ]
        
        for dir_path in dirs:
            (project_dir / dir_path).mkdir(parents=True, exist_ok=True)
        
        # 基础文件内容会在后续方法中生成
    
    def _generate_config_files(self, project_dir: Path, config: WebsiteConfig, content: Dict[str, str]):
        """生成配置文件"""
        
        # next.config.js
        next_config = f'''/** @type {{import('next').NextConfig}} */
const nextConfig = {{
  output: 'export',
  trailingSlash: true,
  images: {{
    unoptimized: true
  }},
  env: {{
    SITE_TITLE: '{content["title"]}',
    SITE_DESCRIPTION: '{content["description"]}',
    SITE_KEYWORDS: '{", ".join(content["meta_keywords"][:5])}'
  }}
}};

module.exports = nextConfig;
'''
        
        with open(project_dir / "next.config.js", "w", encoding="utf-8") as f:
            f.write(next_config)
        
        # tailwind.config.js
        tailwind_config = '''/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        }
      }
    },
  },
  plugins: [],
}
'''
        
        with open(project_dir / "tailwind.config.js", "w") as f:
            f.write(tailwind_config)
    
    def _generate_component_files(self, project_dir: Path, config: WebsiteConfig, content: Dict[str, str]):
        """生成组件文件"""
        
        components_dir = project_dir / "src" / "components"
        
        # Header组件
        header_component = f'''import Link from 'next/link';

export default function Header() {{
  return (
    <header className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <Link href="/" className="text-xl font-bold text-gray-900">
              {content["hero_section"]["headline"]}
            </Link>
          </div>
          <nav className="hidden md:flex space-x-8">
            <Link href="#features" className="text-gray-600 hover:text-gray-900">
              Features
            </Link>
            <Link href="#how-to-use" className="text-gray-600 hover:text-gray-900">
              How to Use
            </Link>
            <Link href="#faq" className="text-gray-600 hover:text-gray-900">
              FAQ
            </Link>
          </nav>
        </div>
      </div>
    </header>
  );
}}
'''
        
        with open(components_dir / "Header.tsx", "w", encoding="utf-8") as f:
            f.write(header_component)
        
        # Footer组件
        footer_component = f'''export default function Footer() {{
  return (
    <footer className="bg-gray-50 border-t">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center text-gray-600">
          <p>{content["footer"]["copyright"]}</p>
          <div className="mt-4 space-x-4">
            <a href="#" className="hover:text-gray-900">{content["footer"]["privacy_policy"]}</a>
            <a href="#" className="hover:text-gray-900">{content["footer"]["terms_of_service"]}</a>
            <a href="#" className="hover:text-gray-900">{content["footer"]["contact"]}</a>
          </div>
        </div>
      </div>
    </footer>
  );
}}
'''
        
        with open(components_dir / "Footer.tsx", "w", encoding="utf-8") as f:
            f.write(footer_component)
        
        # Tool组件 (基础版本)
        tool_component = self._generate_tool_component(config)
        with open(components_dir / f"{config.tool_type.title()}Tool.tsx", "w", encoding="utf-8") as f:
            f.write(tool_component)
    
    def _generate_tool_component(self, config: WebsiteConfig) -> str:
        """生成工具组件"""
        
        tool_type = config.tool_type.lower()
        
        if tool_type in ["converter", "transform"]:
            return self._generate_converter_component(config)
        elif tool_type in ["generator", "maker"]:
            return self._generate_generator_component(config)
        elif tool_type in ["calculator", "compute"]:
            return self._generate_calculator_component(config)
        else:
            return self._generate_generic_tool_component(config)
    
    def _generate_converter_component(self, config: WebsiteConfig) -> str:
        """生成转换器组件"""
        
        return f'''import {{ useState }} from 'react';

export default function ConverterTool() {{
  const [file, setFile] = useState<File | null>(null);
  const [result, setResult] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {{
    if (e.target.files && e.target.files[0]) {{
      setFile(e.target.files[0]);
    }}
  }};

  const handleConvert = async () => {{
    if (!file) return;
    
    setIsProcessing(true);
    
    // TODO: 实现实际的转换逻辑
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setResult('Conversion completed successfully!');
    setIsProcessing(false);
  }};

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-2xl font-bold mb-6">{config.keyword.title()} Converter</h2>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select File
          </label>
          <input
            type="file"
            onChange={{handleFileChange}}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
        </div>
        
        <button
          onClick={{handleConvert}}
          disabled={{!file || isProcessing}}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {{isProcessing ? 'Converting...' : 'Convert'}}
        </button>
        
        {{result && (
          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
            <p className="text-green-800">{{result}}</p>
          </div>
        )}}
      </div>
    </div>
  );
}}
'''
    
    def _generate_generator_component(self, config: WebsiteConfig) -> str:
        """生成生成器组件"""
        
        return f'''import {{ useState }} from 'react';

export default function GeneratorTool() {{
  const [options, setOptions] = useState({{}});
  const [result, setResult] = useState<string>('');
  
  const handleGenerate = () => {{
    // TODO: 实现实际的生成逻辑
    setResult('Generated content will appear here...');
  }};

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-2xl font-bold mb-6">{config.keyword.title()} Generator</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 className="text-lg font-semibold mb-4">Options</h3>
          {{/* TODO: 添加配置选项 */}}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Option 1
              </label>
              <input
                type="text"
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="Enter value..."
              />
            </div>
          </div>
          
          <button
            onClick={{handleGenerate}}
            className="mt-4 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
          >
            Generate
          </button>
        </div>
        
        <div>
          <h3 className="text-lg font-semibold mb-4">Preview</h3>
          <div className="border border-gray-300 rounded-md p-4 min-h-[200px] bg-gray-50">
            <pre className="whitespace-pre-wrap text-sm">{{result || 'Generated content will appear here...'}}</pre>
          </div>
        </div>
      </div>
    </div>
  );
}}
'''
    
    def _generate_calculator_component(self, config: WebsiteConfig) -> str:
        """生成计算器组件"""
        
        return f'''import {{ useState }} from 'react';

export default function CalculatorTool() {{
  const [inputs, setInputs] = useState({{}});
  const [result, setResult] = useState<number | null>(null);
  
  const handleCalculate = () => {{
    // TODO: 实现实际的计算逻辑
    setResult(42); // 示例结果
  }};

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-2xl font-bold mb-6">{config.keyword.title()} Calculator</h2>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Input Value
          </label>
          <input
            type="number"
            className="w-full border border-gray-300 rounded-md px-3 py-2"
            placeholder="Enter value..."
          />
        </div>
        
        <button
          onClick={{handleCalculate}}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
        >
          Calculate
        </button>
        
        {{result !== null && (
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <h3 className="text-lg font-semibold text-blue-800">Result</h3>
            <p className="text-2xl font-bold text-blue-900">{{result}}</p>
          </div>
        )}}
      </div>
    </div>
  );
}}
'''
    
    def _generate_generic_tool_component(self, config: WebsiteConfig) -> str:
        """生成通用工具组件"""
        
        return f'''import {{ useState }} from 'react';

export default function GenericTool() {{
  const [input, setInput] = useState('');
  const [result, setResult] = useState('');
  
  const handleProcess = () => {{
    // TODO: 实现实际的处理逻辑
    setResult('Processing completed!');
  }};

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-2xl font-bold mb-6">{config.keyword.title()} Tool</h2>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Input
          </label>
          <textarea
            value={{input}}
            onChange={{(e) => setInput(e.target.value)}}
            className="w-full border border-gray-300 rounded-md px-3 py-2 h-32"
            placeholder="Enter your input here..."
          />
        </div>
        
        <button
          onClick={{handleProcess}}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
        >
          Process
        </button>
        
        {{result && (
          <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-md">
            <h3 className="text-lg font-semibold mb-2">Result</h3>
            <p>{{result}}</p>
          </div>
        )}}
      </div>
    </div>
  );
}}
'''
    
    def _generate_page_files(self, project_dir: Path, config: WebsiteConfig, content: Dict[str, str]):
        """生成页面文件"""
        
        app_dir = project_dir / "src" / "app"
        
        # layout.tsx
        layout_content = f'''import './globals.css';
import type {{ Metadata }} from 'next';

export const metadata: Metadata = {{
  title: '{content["title"]}',
  description: '{content["description"]}',
  keywords: '{", ".join(content["meta_keywords"])}',
  authors: [{{ name: '{config.keyword.title()} Tool' }}],
  openGraph: {{
    title: '{content["title"]}',
    description: '{content["description"]}',
    type: 'website',
  }},
}};

export default function RootLayout({{
  children,
}}: {{
  children: React.ReactNode;
}}) {{
  return (
    <html lang="{config.language}">
      <body>{{children}}</body>
    </html>
  );
}}
'''
        
        with open(app_dir / "layout.tsx", "w", encoding="utf-8") as f:
            f.write(layout_content)
        
        # page.tsx
        tool_component_name = f"{config.tool_type.title()}Tool"
        page_content = f'''import Header from '../components/Header';
import Footer from '../components/Footer';
import {tool_component_name} from '../components/{tool_component_name}';

export default function Home() {{
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-1">
        {{/* Hero Section */}}
        <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-4">
              {content["hero_section"]["headline"]}
            </h1>
            <p className="text-xl md:text-2xl mb-8">
              {content["hero_section"]["subheadline"]}
            </p>
            <div className="text-lg">
              {content["hero_section"]["features_text"]}
            </div>
          </div>
        </section>
        
        {{/* Tool Section */}}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <{tool_component_name} />
          </div>
        </section>
        
        {{/* Features Section */}}
        <section id="features" className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold text-center mb-12">Features</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {json.dumps(content["features"], indent=6)[6:-6].replace('"', '"').replace('{{', '{').replace('}}', '}').split('},')[:-1]}
            </div>
          </div>
        </section>
        
        {{/* How to Use Section */}}
        <section id="how-to-use" className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold text-center mb-12">How to Use</h2>
            <div className="space-y-6">
              {chr(123)}content["how_to_use"].map((step: string, index: number) => (
                <div key={{index}} className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
                    {{index + 1}}
                  </div>
                  <p className="text-lg text-gray-700">{{step}}</p>
                </div>
              )){chr(125)}
            </div>
          </div>
        </section>
        
        {{/* FAQ Section */}}
        <section id="faq" className="py-16 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold text-center mb-12">Frequently Asked Questions</h2>
            <div className="space-y-6">
              {chr(123)}content["faq"].map((item: any, index: number) => (
                <div key={{index}} className="bg-white rounded-lg p-6 shadow-sm">
                  <h3 className="text-lg font-semibold mb-2">{{item.question}}</h3>
                  <p className="text-gray-600">{{item.answer}}</p>
                </div>
              )){chr(125)}
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
}}

// 静态数据
const content = {json.dumps(content, indent=2, ensure_ascii=False)};
'''
        
        with open(app_dir / "page.tsx", "w", encoding="utf-8") as f:
            f.write(page_content)
        
        # globals.css
        css_content = '''@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .btn-primary {
    @apply bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }
}
'''
        
        with open(app_dir / "globals.css", "w") as f:
            f.write(css_content)
    
    def _update_package_json(self, project_dir: Path, config: WebsiteConfig):
        """更新package.json"""
        
        package_json = {
            "name": f"{config.keyword.replace(' ', '-').lower()}-tool",
            "version": "0.1.0",
            "private": True,
            "scripts": {
                "dev": "next dev",
                "build": "next build",
                "start": "next start",
                "lint": "next lint",
                "export": "next build && next export"
            },
            "dependencies": {
                "next": "14.0.0",
                "react": "18.0.0",
                "react-dom": "18.0.0"
            },
            "devDependencies": {
                "@types/node": "20.0.0",
                "@types/react": "18.0.0",
                "@types/react-dom": "18.0.0",
                "autoprefixer": "10.0.0",
                "eslint": "8.0.0",
                "eslint-config-next": "14.0.0",
                "postcss": "8.0.0",
                "tailwindcss": "3.0.0",
                "typescript": "5.0.0"
            }
        }
        
        with open(project_dir / "package.json", "w") as f:
            json.dump(package_json, f, indent=2)
    
    def _get_generated_files_list(self, project_dir: Path) -> List[str]:
        """获取生成的文件列表"""
        files = []
        for file_path in project_dir.rglob("*"):
            if file_path.is_file():
                files.append(str(file_path.relative_to(project_dir)))
        return sorted(files)