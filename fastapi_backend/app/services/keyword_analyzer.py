"""关键词分析服务 - 集成多个数据源进行关键词分析"""

import asyncio
import httpx
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class KeywordMetrics:
    """关键词指标数据类"""
    keyword: str
    search_volume: Optional[int] = None
    keyword_difficulty: Optional[float] = None
    cpc: Optional[float] = None
    competition: Optional[float] = None
    search_intent: Optional[str] = None
    zero_click_rate: Optional[float] = None
    trend_score: Optional[float] = None
    trend_data: Optional[Dict[str, Any]] = None
    serp_quality_score: Optional[float] = None
    serp_analysis: Optional[Dict[str, Any]] = None


class SEMrushAPI:
    """SEMrush API 集成"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.semrush.com"
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def get_keyword_overview(self, keyword: str, database: str = "us") -> Dict[str, Any]:
        """获取关键词概览数据"""
        try:
            params = {
                "type": "phrase_this",
                "key": self.api_key,
                "phrase": keyword,
                "database": database,
                "export_columns": "Ph,Nq,Cp,Co,Nr,Td"
            }
            
            response = await self.client.get(f"{self.base_url}/", params=params)
            response.raise_for_status()
            
            lines = response.text.strip().split('\n')
            if len(lines) < 2:
                return {}
            
            data = lines[1].split(';')
            if len(data) >= 6:
                return {
                    "keyword": data[0],
                    "search_volume": int(data[1]) if data[1].isdigit() else 0,
                    "cpc": float(data[2]) if data[2] else 0.0,
                    "competition": float(data[3]) if data[3] else 0.0,
                    "results_count": int(data[4]) if data[4].isdigit() else 0,
                    "trends": data[5] if len(data) > 5 else ""
                }
        except Exception as e:
            logger.error(f"SEMrush API error for keyword '{keyword}': {str(e)}")
            return {}
    
    async def get_keyword_difficulty(self, keyword: str, database: str = "us") -> float:
        """获取关键词难度"""
        try:
            params = {
                "type": "phrase_kdi",
                "key": self.api_key,
                "phrase": keyword,
                "database": database
            }
            
            response = await self.client.get(f"{self.base_url}/", params=params)
            response.raise_for_status()
            
            lines = response.text.strip().split('\n')
            if len(lines) >= 2:
                difficulty = lines[1].split(';')[1]
                return float(difficulty) if difficulty else 0.0
        except Exception as e:
            logger.error(f"SEMrush KD error for keyword '{keyword}': {str(e)}")
            return 0.0


class GoogleTrendsAPI:
    """Google Trends API 集成"""
    
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
        self._pytrends = None
    
    def _get_pytrends(self):
        """延迟加载pytrends"""
        if self._pytrends is None:
            try:
                from pytrends.request import TrendReq
                self._pytrends = TrendReq(hl='en-US', tz=360)
            except ImportError:
                logger.warning("pytrends not installed, using simulated data")
                self._pytrends = False
        return self._pytrends
    
    async def get_trend_data(self, keyword: str, timeframe: str = "today 12-m", geo: str = "US") -> Dict[str, Any]:
        """获取趋势数据"""
        try:
            pytrends = self._get_pytrends()
            
            if pytrends and pytrends is not False:
                # 使用真实的Google Trends API
                trend_data = await asyncio.to_thread(self._fetch_real_trends, keyword, timeframe, geo)
                if trend_data:
                    return trend_data
            
            # 回退到模拟数据
            return await self._get_simulated_trends(keyword, timeframe)
            
        except Exception as e:
            logger.error(f"Google Trends error for keyword '{keyword}': {str(e)}")
            return await self._get_simulated_trends(keyword, timeframe)
    
    def _fetch_real_trends(self, keyword: str, timeframe: str, geo: str) -> Dict[str, Any]:
        """获取真实趋势数据"""
        try:
            pytrends = self._get_pytrends()
            pytrends.build_payload([keyword], cat=0, timeframe=timeframe, geo=geo, gprop='')
            
            # 获取兴趣随时间变化
            interest_over_time = pytrends.interest_over_time()
            
            if not interest_over_time.empty:
                trend_values = interest_over_time[keyword].tolist()
                
                # 计算趋势斜率
                if len(trend_values) >= 2:
                    slope = (trend_values[-1] - trend_values[0]) / len(trend_values)
                    if slope > 2:
                        trend_score = 1.0
                    elif slope > -2:
                        trend_score = 0.5
                    else:
                        trend_score = 0.0
                else:
                    trend_score = 0.5
                
                return {
                    "keyword": keyword,
                    "trend_values": trend_values,
                    "trend_score": trend_score,
                    "timeframe": timeframe,
                    "average_interest": sum(trend_values) / len(trend_values),
                    "data_source": "google_trends_real"
                }
        except Exception as e:
            logger.error(f"Error fetching real trends for '{keyword}': {str(e)}")
            return None
    
    async def _get_simulated_trends(self, keyword: str, timeframe: str) -> Dict[str, Any]:
        """获取模拟趋势数据"""
        import random
        
        # 根据关键词类型生成更真实的模拟数据
        base_interest = 50
        if "online" in keyword.lower() or "tool" in keyword.lower():
            base_interest = 60  # 在线工具通常有较高关注度
        elif "converter" in keyword.lower():
            base_interest = 70  # 转换工具很受欢迎
        
        # 生成有一定波动的趋势数据
        trend_values = []
        current_value = base_interest
        for i in range(12):
            # 添加随机波动但保持趋势
            change = random.randint(-10, 15)  # 略微偏向上升
            current_value = max(10, min(100, current_value + change))
            trend_values.append(current_value)
        
        # 计算趋势斜率
        if len(trend_values) >= 2:
            slope = (trend_values[-1] - trend_values[0]) / len(trend_values)
            if slope > 2:
                trend_score = 1.0
            elif slope > -2:
                trend_score = 0.5
            else:
                trend_score = 0.0
        else:
            trend_score = 0.5
        
        return {
            "keyword": keyword,
            "trend_values": trend_values,
            "trend_score": trend_score,
            "timeframe": timeframe,
            "average_interest": sum(trend_values) / len(trend_values),
            "data_source": "simulated"
        }


class SERPAnalyzer:
    """SERP 分析器"""
    
    def __init__(self, use_real_search: bool = False):
        self.use_real_search = use_real_search
        self.client = httpx.AsyncClient(
            timeout=30.0,
            headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }
        )
        
        # 已知的UGC平台和弱竞争对手指标
        self.ugc_domains = {
            "reddit.com", "quora.com", "stackoverflow.com", "medium.com", 
            "csdn.net", "zhihu.com", "jianshu.com", "github.com"
        }
        self.weak_indicators = {
            "blogspot.com", "wordpress.com", "wix.com", "squarespace.com",
            "weebly.com", "tumblr.com"
        }
    
    async def analyze_serp(self, keyword: str, country: str = "us") -> Dict[str, Any]:
        """分析搜索结果页面质量"""
        try:
            if self.use_real_search:
                # 尝试使用真实搜索（需要配置搜索API）
                serp_data = await self._fetch_real_serp(keyword, country)
                if serp_data:
                    return serp_data
            
            # 回退到智能模拟
            return await self._generate_intelligent_serp(keyword)
            
        except Exception as e:
            logger.error(f"SERP analysis error for keyword '{keyword}': {str(e)}")
            return await self._generate_intelligent_serp(keyword)
    
    async def _fetch_real_serp(self, keyword: str, country: str) -> Optional[Dict[str, Any]]:
        """获取真实SERP数据（需要配置搜索API）"""
        try:
            # 这里可以集成 SerpAPI, ScrapingBee, 或其他搜索API
            # 示例使用 Google Custom Search API (需要API密钥)
            
            # 为了演示，这里返回None，表示需要配置真实API
            return None
            
        except Exception as e:
            logger.error(f"Error fetching real SERP for '{keyword}': {str(e)}")
            return None
    
    async def _generate_intelligent_serp(self, keyword: str) -> Dict[str, Any]:
        """生成智能化的模拟SERP数据"""
        import random
        
        # 根据关键词类型生成更真实的SERP结果
        keyword_lower = keyword.lower()
        
        # 定义不同类型关键词的SERP特征
        serp_results = []
        
        # 工具类关键词通常会有一些大型工具站占据前几位
        if any(word in keyword_lower for word in ["converter", "generator", "calculator", "tool"]):
            # 前3位通常是成熟的工具站
            major_tools = ["smallpdf.com", "canva.com", "convertio.co", "online-convert.com", "tools.org"]
            for i in range(3):
                domain = random.choice(major_tools) if i < 2 else f"tool{i+1}.com"
                serp_results.append({
                    "position": i + 1,
                    "domain": domain,
                    "title": f"{keyword.title()} - {domain.split('.')[0].title()}",
                    "url": f"https://{domain}/{keyword.replace(' ', '-')}",
                    "site_type": "tool",
                    "domain_strength": "high" if i < 2 else "medium",
                    "content_age": "recent",
                    "estimated_da": random.randint(60, 95) if i < 2 else random.randint(40, 70)
                })
            
            # 4-10位混合各种类型
            mixed_types = ["article", "forum", "wiki", "commercial"]
            for i in range(3, 10):
                site_type = random.choice(mixed_types)
                if site_type == "forum":
                    domain = random.choice(["reddit.com", "quora.com", "stackoverflow.com"])
                    strength = "medium"
                elif site_type == "wiki":
                    domain = "wikipedia.org"
                    strength = "high"
                elif site_type == "article":
                    domain = f"blog{i}.com"
                    strength = random.choice(["low", "medium"])
                else:
                    domain = f"service{i}.com"
                    strength = random.choice(["medium", "high"])
                
                serp_results.append({
                    "position": i + 1,
                    "domain": domain,
                    "title": f"How to use {keyword} - {domain.split('.')[0].title()}",
                    "url": f"https://{domain}/articles/{keyword.replace(' ', '-')}",
                    "site_type": site_type,
                    "domain_strength": strength,
                    "content_age": random.choice(["recent", "old"]),
                    "estimated_da": random.randint(30, 85)
                })
        
        else:
            # 其他类型关键词的通用SERP生成
            site_types = ["article", "commercial", "forum", "wiki"]
            for i in range(10):
                site_type = random.choice(site_types)
                domain = f"example{i+1}.com"
                
                serp_results.append({
                    "position": i + 1,
                    "domain": domain,
                    "title": f"{keyword.title()} - Example {i+1}",
                    "url": f"https://{domain}/{keyword.replace(' ', '-')}",
                    "site_type": site_type,
                    "domain_strength": random.choice(["low", "medium", "high"]),
                    "content_age": random.choice(["recent", "old", "very_old"]),
                    "estimated_da": random.randint(20, 80)
                })
        
        # 计算质量分数
        quality_score = self._calculate_serp_quality_score(serp_results)
        
        # 生成详细分析
        analysis = self._generate_serp_analysis(serp_results, keyword)
        
        return {
            "keyword": keyword,
            "serp_results": serp_results,
            "quality_score": quality_score,
            "analysis": analysis,
            "opportunities": self._identify_opportunities(serp_results),
            "analysis_date": datetime.utcnow().isoformat(),
            "data_source": "intelligent_simulation"
        }
    
    def _generate_serp_analysis(self, serp_results: List[Dict], keyword: str) -> Dict[str, Any]:
        """生成SERP分析报告"""
        analysis = {
            "total_results": len(serp_results),
            "site_type_distribution": {},
            "domain_strength_distribution": {},
            "content_age_distribution": {},
            "average_da": 0,
            "ugc_platforms_count": 0,
            "weak_domains_count": 0
        }
        
        # 统计分布
        for result in serp_results:
            # 网站类型分布
            site_type = result.get("site_type", "unknown")
            analysis["site_type_distribution"][site_type] = analysis["site_type_distribution"].get(site_type, 0) + 1
            
            # 域名强度分布
            strength = result.get("domain_strength", "unknown")
            analysis["domain_strength_distribution"][strength] = analysis["domain_strength_distribution"].get(strength, 0) + 1
            
            # 内容年龄分布
            age = result.get("content_age", "unknown")
            analysis["content_age_distribution"][age] = analysis["content_age_distribution"].get(age, 0) + 1
            
            # DA统计
            da = result.get("estimated_da", 0)
            analysis["average_da"] += da
            
            # UGC平台统计
            domain = result.get("domain", "")
            if any(ugc in domain for ugc in self.ugc_domains):
                analysis["ugc_platforms_count"] += 1
            
            # 弱域名统计
            if any(weak in domain for weak in self.weak_indicators):
                analysis["weak_domains_count"] += 1
        
        analysis["average_da"] = analysis["average_da"] / len(serp_results) if serp_results else 0
        
        return analysis
    
    def _identify_opportunities(self, serp_results: List[Dict]) -> List[Dict[str, Any]]:
        """识别SEO机会"""
        opportunities = []
        
        # 检查UGC平台机会
        ugc_count = sum(1 for result in serp_results if any(ugc in result.get("domain", "") for ugc in self.ugc_domains))
        if ugc_count >= 2:
            opportunities.append({
                "type": "ugc_displacement",
                "description": f"发现{ugc_count}个UGC平台在前10位，有机会通过专业工具站替代",
                "priority": "high"
            })
        
        # 检查弱域名机会
        weak_count = sum(1 for result in serp_results if result.get("domain_strength") == "low")
        if weak_count >= 3:
            opportunities.append({
                "type": "weak_competition",
                "description": f"发现{weak_count}个弱域名在前10位，竞争相对较弱",
                "priority": "medium"
            })
        
        # 检查内容陈旧机会
        old_content_count = sum(1 for result in serp_results if result.get("content_age") in ["old", "very_old"])
        if old_content_count >= 4:
            opportunities.append({
                "type": "content_freshness",
                "description": f"发现{old_content_count}个陈旧内容，可通过新鲜内容获得优势",
                "priority": "medium"
            })
        
        return opportunities
    
    def _calculate_serp_quality_score(self, serp_results: List[Dict]) -> float:
        """计算SERP质量分数"""
        score = 0.0
        
        for result in serp_results[:10]:  # 只看前10个结果
            # 如果发现UGC平台或老旧内容，增加机会分数
            if result["site_type"] in ["forum", "wiki"]:
                score += 0.1
            if result["content_age"] in ["old", "very_old"]:
                score += 0.08
            if result["domain_strength"] == "low":
                score += 0.05
        
        return min(score, 1.0)


class KeywordAnalyzer:
    """关键词分析器主类"""
    
    def __init__(self, semrush_api_key: Optional[str] = None):
        self.semrush = SEMrushAPI(semrush_api_key) if semrush_api_key else None
        self.google_trends = GoogleTrendsAPI()
        self.serp_analyzer = SERPAnalyzer()
    
    async def analyze_keyword(self, keyword: str, language: str = "en", country: str = "us") -> KeywordMetrics:
        """分析单个关键词"""
        
        # 并发执行多个分析任务
        tasks = []
        
        # SEMrush 数据
        if self.semrush:
            tasks.append(self.semrush.get_keyword_overview(keyword, country))
            tasks.append(self.semrush.get_keyword_difficulty(keyword, country))
        
        # Google Trends 数据
        tasks.append(self.google_trends.get_trend_data(keyword))
        
        # SERP 分析
        tasks.append(self.serp_analyzer.analyze_serp(keyword))
        
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 解析结果
            semrush_overview = results[0] if len(results) > 0 and isinstance(results[0], dict) else {}
            semrush_kd = results[1] if len(results) > 1 and isinstance(results[1], (int, float)) else 0.0
            trends_data = results[2] if len(results) > 2 and isinstance(results[2], dict) else {}
            serp_data = results[3] if len(results) > 3 and isinstance(results[3], dict) else {}
            
            # 构建关键词指标
            metrics = KeywordMetrics(
                keyword=keyword,
                search_volume=semrush_overview.get("search_volume"),
                keyword_difficulty=semrush_kd,
                cpc=semrush_overview.get("cpc"),
                competition=semrush_overview.get("competition"),
                search_intent=self._determine_search_intent(keyword),
                trend_score=trends_data.get("trend_score"),
                trend_data=trends_data,
                serp_quality_score=serp_data.get("quality_score"),
                serp_analysis=serp_data
            )
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error analyzing keyword '{keyword}': {str(e)}")
            return KeywordMetrics(keyword=keyword)
    
    async def analyze_keywords_batch(self, keywords: List[str], language: str = "en", country: str = "us") -> List[KeywordMetrics]:
        """批量分析关键词"""
        
        # 限制并发数量以避免API限制
        semaphore = asyncio.Semaphore(5)
        
        async def analyze_with_semaphore(keyword):
            async with semaphore:
                return await self.analyze_keyword(keyword, language, country)
        
        tasks = [analyze_with_semaphore(keyword) for keyword in keywords]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤异常结果
        valid_results = []
        for result in results:
            if isinstance(result, KeywordMetrics):
                valid_results.append(result)
            else:
                logger.error(f"Error in batch analysis: {result}")
        
        return valid_results
    
    def _determine_search_intent(self, keyword: str) -> str:
        """确定搜索意图"""
        keyword_lower = keyword.lower()
        
        # 交易型关键词
        if any(word in keyword_lower for word in ["buy", "purchase", "price", "cost", "cheap", "best", "review"]):
            return "transactional"
        
        # 商业型关键词
        elif any(word in keyword_lower for word in ["compare", "vs", "alternative", "tool", "software", "app"]):
            return "commercial"
        
        # 信息型关键词
        elif any(word in keyword_lower for word in ["how", "what", "why", "when", "where", "guide", "tutorial"]):
            return "informational"
        
        # 导航型关键词
        elif any(word in keyword_lower for word in ["login", "sign in", "website", "official", "homepage"]):
            return "navigational"
        
        # 默认为信息型
        return "informational"
    
    def calculate_potential_score(self, metrics: KeywordMetrics, weights: Optional[Dict[str, float]] = None) -> float:
        """计算关键词潜力分数"""
        
        if weights is None:
            weights = {
                "volume": 0.3,
                "difficulty": 0.3,
                "cpc": 0.2,
                "trend": 0.1,
                "serp_quality": 0.1
            }
        
        score = 0.0
        
        # 搜索量评分 (归一化到0-1)
        if metrics.search_volume is not None:
            volume_score = min(metrics.search_volume / 10000, 1.0)  # 假设10000为满分
            score += weights["volume"] * volume_score
        
        # 难度评分 (反向评分，难度越低分数越高)
        if metrics.keyword_difficulty is not None:
            difficulty_score = max(0, (100 - metrics.keyword_difficulty) / 100)
            score += weights["difficulty"] * difficulty_score
        
        # CPC评分 (归一化到0-1)
        if metrics.cpc is not None:
            cpc_score = min(metrics.cpc / 10, 1.0)  # 假设10美元为满分
            score += weights["cpc"] * cpc_score
        
        # 趋势评分
        if metrics.trend_score is not None:
            score += weights["trend"] * metrics.trend_score
        
        # SERP质量评分
        if metrics.serp_quality_score is not None:
            score += weights["serp_quality"] * metrics.serp_quality_score
        
        return min(score, 1.0)


class KeywordExpander:
    """关键词扩展器"""
    
    def __init__(self):
        self.common_modifiers = {
            "tools": ["online", "free", "best", "tool", "generator", "converter", "calculator"],
            "converters": ["online", "free", "to", "converter", "convert"],
            "generators": ["generator", "maker", "creator", "builder", "online", "free"],
            "calculators": ["calculator", "calculate", "estimation", "online", "free"]
        }
    
    def expand_seed_keywords(self, seed_keywords: List[str], category: str = "tools") -> List[str]:
        """扩展种子关键词"""
        expanded = []
        modifiers = self.common_modifiers.get(category, self.common_modifiers["tools"])
        
        for seed in seed_keywords:
            # 原始关键词
            expanded.append(seed)
            
            # 添加前缀修饰词
            for modifier in modifiers:
                if modifier not in seed.lower():
                    expanded.append(f"{modifier} {seed}")
                    expanded.append(f"{seed} {modifier}")
        
        # 去重并返回
        return list(set(expanded))
    
    def generate_keyword_combinations(self, base_words: List[str], modifiers: List[str]) -> List[str]:
        """生成关键词组合"""
        combinations = []
        
        for base in base_words:
            for modifier in modifiers:
                combinations.extend([
                    f"{modifier} {base}",
                    f"{base} {modifier}",
                    f"{modifier}{base}",  # 连写
                ])
        
        return list(set(combinations))