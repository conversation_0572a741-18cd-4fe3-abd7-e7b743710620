"""自动部署服务 - GitHub Actions + Vercel 集成"""

import os
import json
import subprocess
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging
import tempfile
import shutil
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class DeploymentConfig:
    """部署配置"""
    project_name: str
    project_path: str
    github_token: str
    github_username: str
    vercel_token: str
    domain: Optional[str] = None
    subdomain: Optional[str] = None


@dataclass
class DeploymentResult:
    """部署结果"""
    success: bool
    github_repo_url: Optional[str] = None
    vercel_deployment_url: Optional[str] = None
    error_message: Optional[str] = None
    logs: List[str] = None


class GitHubService:
    """GitHub 服务"""
    
    def __init__(self, token: str, username: str):
        self.token = token
        self.username = username
    
    async def create_repository(self, repo_name: str, description: str = "") -> Dict[str, Any]:
        """创建GitHub仓库"""
        
        import httpx
        
        headers = {
            "Authorization": f"token {self.token}",
            "Accept": "application/vnd.github.v3+json"
        }
        
        data = {
            "name": repo_name,
            "description": description,
            "private": True,  # 创建私有仓库
            "auto_init": False
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://api.github.com/user/repos",
                headers=headers,
                json=data
            )
            
            if response.status_code == 201:
                return response.json()
            else:
                logger.error(f"Failed to create GitHub repository: {response.text}")
                raise Exception(f"GitHub API error: {response.status_code}")
    
    def init_and_push_repository(self, project_path: str, repo_url: str) -> bool:
        """初始化并推送代码到GitHub"""
        
        try:
            # 进入项目目录
            os.chdir(project_path)
            
            # 初始化Git仓库
            subprocess.run(["git", "init"], check=True)
            
            # 添加所有文件
            subprocess.run(["git", "add", "."], check=True)
            
            # 提交代码
            subprocess.run([
                "git", "commit", "-m", "Initial commit: Auto-generated tool website"
            ], check=True)
            
            # 设置远程仓库
            subprocess.run(["git", "remote", "add", "origin", repo_url], check=True)
            
            # 推送到main分支
            subprocess.run([
                "git", "push", "-u", "origin", "main"
            ], check=True)
            
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Git operation failed: {e}")
            return False
        except Exception as e:
            logger.error(f"Error in git operations: {str(e)}")
            return False


class VercelService:
    """Vercel 服务"""
    
    def __init__(self, token: str):
        self.token = token
    
    async def deploy_project(self, project_name: str, github_repo_url: str) -> Dict[str, Any]:
        """部署项目到Vercel"""
        
        import httpx
        
        headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }
        
        # 从GitHub仓库URL提取用户名和仓库名
        repo_parts = github_repo_url.replace("https://github.com/", "").replace(".git", "").split("/")
        github_username = repo_parts[0]
        repo_name = repo_parts[1]
        
        data = {
            "name": project_name,
            "gitRepository": {
                "type": "github",
                "repo": f"{github_username}/{repo_name}"
            },
            "buildCommand": "npm run build",
            "outputDirectory": "out",
            "installCommand": "npm install",
            "framework": "nextjs"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://api.vercel.com/v10/projects",
                headers=headers,
                json=data
            )
            
            if response.status_code in [200, 201]:
                return response.json()
            else:
                logger.error(f"Failed to create Vercel project: {response.text}")
                raise Exception(f"Vercel API error: {response.status_code}")
    
    async def get_deployment_status(self, deployment_id: str) -> Dict[str, Any]:
        """获取部署状态"""
        
        import httpx
        
        headers = {
            "Authorization": f"Bearer {self.token}"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"https://api.vercel.com/v13/deployments/{deployment_id}",
                headers=headers
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get deployment status: {response.text}")
                raise Exception(f"Vercel API error: {response.status_code}")


class GitHubActionsGenerator:
    """GitHub Actions 工作流生成器"""
    
    @staticmethod
    def generate_nextjs_workflow() -> str:
        """生成Next.js项目的GitHub Actions工作流"""
        
        return """name: Deploy to Vercel

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test --if-present
    
    - name: Build project
      run: npm run build
    
    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
        vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
        working-directory: ./
"""
    
    @staticmethod
    def create_workflow_file(project_path: str) -> None:
        """在项目中创建GitHub Actions工作流文件"""
        
        workflows_dir = Path(project_path) / ".github" / "workflows"
        workflows_dir.mkdir(parents=True, exist_ok=True)
        
        workflow_content = GitHubActionsGenerator.generate_nextjs_workflow()
        
        with open(workflows_dir / "deploy.yml", "w") as f:
            f.write(workflow_content)


class DeploymentService:
    """部署服务主类"""
    
    def __init__(self):
        self.github_token = os.getenv("GITHUB_TOKEN")
        self.github_username = os.getenv("GITHUB_USERNAME")
        self.vercel_token = os.getenv("VERCEL_TOKEN")
        
        if not all([self.github_token, self.github_username, self.vercel_token]):
            logger.warning("Deployment tokens not configured. Deployment will be disabled.")
            self.enabled = False
        else:
            self.github_service = GitHubService(self.github_token, self.github_username)
            self.vercel_service = VercelService(self.vercel_token)
            self.enabled = True
    
    async def deploy_website(self, config: DeploymentConfig) -> DeploymentResult:
        """部署网站到GitHub和Vercel"""
        
        if not self.enabled:
            return DeploymentResult(
                success=False,
                error_message="Deployment service not configured"
            )
        
        logs = []
        
        try:
            # 1. 创建GitHub仓库
            logs.append("Creating GitHub repository...")
            
            repo_data = await self.github_service.create_repository(
                repo_name=config.project_name,
                description=f"Auto-generated tool website for {config.project_name}"
            )
            
            github_repo_url = repo_data["clone_url"]
            logs.append(f"GitHub repository created: {github_repo_url}")
            
            # 2. 生成GitHub Actions工作流
            logs.append("Generating GitHub Actions workflow...")
            GitHubActionsGenerator.create_workflow_file(config.project_path)
            
            # 3. 推送代码到GitHub
            logs.append("Pushing code to GitHub...")
            
            push_success = self.github_service.init_and_push_repository(
                config.project_path,
                github_repo_url
            )
            
            if not push_success:
                raise Exception("Failed to push code to GitHub")
            
            logs.append("Code pushed to GitHub successfully")
            
            # 4. 在Vercel上创建项目
            logs.append("Creating Vercel project...")
            
            vercel_project = await self.vercel_service.deploy_project(
                project_name=config.project_name,
                github_repo_url=github_repo_url
            )
            
            vercel_url = f"https://{config.project_name}.vercel.app"
            logs.append(f"Vercel project created: {vercel_url}")
            
            return DeploymentResult(
                success=True,
                github_repo_url=github_repo_url,
                vercel_deployment_url=vercel_url,
                logs=logs
            )
            
        except Exception as e:
            error_msg = f"Deployment failed: {str(e)}"
            logger.error(error_msg)
            logs.append(error_msg)
            
            return DeploymentResult(
                success=False,
                error_message=error_msg,
                logs=logs
            )
    
    async def check_deployment_status(self, deployment_url: str) -> Dict[str, Any]:
        """检查部署状态"""
        
        try:
            import httpx
            
            async with httpx.AsyncClient() as client:
                response = await client.get(deployment_url, timeout=10.0)
                
                if response.status_code == 200:
                    return {
                        "status": "live",
                        "accessible": True,
                        "status_code": response.status_code
                    }
                else:
                    return {
                        "status": "error",
                        "accessible": False,
                        "status_code": response.status_code
                    }
        
        except Exception as e:
            return {
                "status": "error",
                "accessible": False,
                "error": str(e)
            }
    
    def generate_deployment_config(
        self,
        project_name: str,
        project_path: str,
        domain: Optional[str] = None
    ) -> DeploymentConfig:
        """生成部署配置"""
        
        # 清理项目名称，确保符合GitHub和Vercel命名规范
        clean_name = project_name.lower().replace(" ", "-").replace("_", "-")
        clean_name = "".join(c for c in clean_name if c.isalnum() or c == "-")
        
        return DeploymentConfig(
            project_name=clean_name,
            project_path=project_path,
            github_token=self.github_token,
            github_username=self.github_username,
            vercel_token=self.vercel_token,
            domain=domain
        )


class ContinuousDeployment:
    """持续部署管理"""
    
    def __init__(self, deployment_service: DeploymentService):
        self.deployment_service = deployment_service
    
    async def setup_auto_deployment(
        self,
        project_path: str,
        webhook_url: Optional[str] = None
    ) -> Dict[str, Any]:
        """设置自动部署"""
        
        try:
            # 创建部署配置文件
            deploy_config = {
                "auto_deploy": True,
                "triggers": ["push_to_main", "content_update"],
                "webhook_url": webhook_url,
                "build_command": "npm run build",
                "output_directory": "out"
            }
            
            config_path = Path(project_path) / "deploy.config.json"
            with open(config_path, "w") as f:
                json.dump(deploy_config, f, indent=2)
            
            # 创建部署脚本
            deploy_script = """#!/bin/bash

set -e

echo "Starting automated deployment..."

# Install dependencies
npm ci

# Run tests
npm test --if-present

# Build project
npm run build

# Deploy to Vercel
npx vercel --prod --token $VERCEL_TOKEN

echo "Deployment completed successfully!"
"""
            
            script_path = Path(project_path) / "deploy.sh"
            with open(script_path, "w") as f:
                f.write(deploy_script)
            
            # 使脚本可执行
            os.chmod(script_path, 0o755)
            
            return {
                "success": True,
                "config_file": str(config_path),
                "deploy_script": str(script_path)
            }
            
        except Exception as e:
            logger.error(f"Failed to setup auto deployment: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def trigger_deployment(self, project_path: str) -> Dict[str, Any]:
        """触发部署"""
        
        try:
            # 执行部署脚本
            result = subprocess.run(
                ["./deploy.sh"],
                cwd=project_path,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            if result.returncode == 0:
                return {
                    "success": True,
                    "output": result.stdout,
                    "logs": result.stdout.split('\n')
                }
            else:
                return {
                    "success": False,
                    "error": result.stderr,
                    "output": result.stdout
                }
                
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "error": "Deployment timed out"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }