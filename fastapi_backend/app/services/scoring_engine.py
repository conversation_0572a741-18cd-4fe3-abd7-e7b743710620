"""关键词潜力评分引擎 - 高级评分算法"""

import math
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class SearchIntent(Enum):
    """搜索意图枚举"""
    INFORMATIONAL = "informational"
    COMMERCIAL = "commercial" 
    TRANSACTIONAL = "transactional"
    NAVIGATIONAL = "navigational"


class ToolComplexity(Enum):
    """工具复杂度枚举"""
    VERY_SIMPLE = 1  # 简单转换器、计算器
    SIMPLE = 2       # 基础工具
    MODERATE = 3     # 中等复杂度
    COMPLEX = 4      # 复杂工具
    VERY_COMPLEX = 5 # 高度复杂工具


@dataclass
class ScoringWeights:
    """评分权重配置"""
    volume: float = 0.25      # 搜索量权重
    difficulty: float = 0.25  # 关键词难度权重
    commercial: float = 0.20  # 商业价值权重
    opportunity: float = 0.15 # 机会评分权重
    feasibility: float = 0.15 # 可行性评分权重


@dataclass
class KeywordMetrics:
    """关键词指标"""
    keyword: str
    search_volume: Optional[int] = None
    keyword_difficulty: Optional[float] = None
    cpc: Optional[float] = None
    competition: Optional[float] = None
    search_intent: Optional[str] = None
    zero_click_rate: Optional[float] = None
    trend_score: Optional[float] = None
    serp_quality_score: Optional[float] = None
    tool_complexity: Optional[int] = None
    tool_category: Optional[str] = None


class ScoringEngine:
    """关键词潜力评分引擎"""
    
    def __init__(self, weights: Optional[ScoringWeights] = None):
        self.weights = weights or ScoringWeights()
        
        # 评分基准值
        self.benchmarks = {
            "high_volume": 10000,      # 高搜索量基准
            "medium_volume": 1000,     # 中等搜索量基准
            "low_difficulty": 30,      # 低难度基准
            "medium_difficulty": 60,   # 中等难度基准
            "high_cpc": 5.0,          # 高CPC基准
            "medium_cpc": 1.0,        # 中等CPC基准
        }
    
    def calculate_potential_score(self, metrics: KeywordMetrics) -> Dict[str, Any]:
        """计算综合潜力评分"""
        
        # 1. 搜索量评分 (0-1)
        volume_score = self._calculate_volume_score(metrics.search_volume)
        
        # 2. 难度评分 (0-1, 难度越低分数越高)
        difficulty_score = self._calculate_difficulty_score(metrics.keyword_difficulty)
        
        # 3. 商业价值评分 (0-1)
        commercial_score = self._calculate_commercial_score(metrics)
        
        # 4. 机会评分 (0-1, 基于SERP质量和趋势)
        opportunity_score = self._calculate_opportunity_score(metrics)
        
        # 5. 可行性评分 (0-1, 基于工具复杂度)
        feasibility_score = self._calculate_feasibility_score(metrics)
        
        # 计算加权总分
        total_score = (
            self.weights.volume * volume_score +
            self.weights.difficulty * difficulty_score +
            self.weights.commercial * commercial_score +
            self.weights.opportunity * opportunity_score +
            self.weights.feasibility * feasibility_score
        )
        
        # 应用修正因子
        adjusted_score = self._apply_adjustments(total_score, metrics)
        
        # 确保分数在0-1范围内
        final_score = max(0.0, min(1.0, adjusted_score))
        
        return {
            "potential_score": final_score,
            "breakdown": {
                "volume_score": volume_score,
                "difficulty_score": difficulty_score,
                "commercial_score": commercial_score,
                "opportunity_score": opportunity_score,
                "feasibility_score": feasibility_score
            },
            "grade": self._get_score_grade(final_score),
            "recommendation": self._get_recommendation(final_score, metrics)
        }
    
    def _calculate_volume_score(self, volume: Optional[int]) -> float:
        """计算搜索量评分"""
        if volume is None or volume <= 0:
            return 0.0
        
        # 使用对数函数平滑处理搜索量
        if volume >= self.benchmarks["high_volume"]:
            return 1.0
        elif volume >= self.benchmarks["medium_volume"]:
            # 在中等和高搜索量之间使用对数插值
            log_ratio = math.log(volume / self.benchmarks["medium_volume"]) / \
                       math.log(self.benchmarks["high_volume"] / self.benchmarks["medium_volume"])
            return 0.5 + 0.5 * log_ratio
        else:
            # 低搜索量线性评分
            return 0.5 * (volume / self.benchmarks["medium_volume"])
    
    def _calculate_difficulty_score(self, difficulty: Optional[float]) -> float:
        """计算难度评分 (难度越低分数越高)"""
        if difficulty is None:
            return 0.5  # 默认中等分数
        
        if difficulty <= self.benchmarks["low_difficulty"]:
            return 1.0
        elif difficulty <= self.benchmarks["medium_difficulty"]:
            # 在低难度和中等难度之间线性插值
            ratio = (difficulty - self.benchmarks["low_difficulty"]) / \
                   (self.benchmarks["medium_difficulty"] - self.benchmarks["low_difficulty"])
            return 1.0 - 0.3 * ratio  # 从1.0降到0.7
        else:
            # 高难度评分更低
            excess = difficulty - self.benchmarks["medium_difficulty"]
            penalty = min(0.6, excess / 100)  # 最多扣0.6分
            return max(0.1, 0.7 - penalty)
    
    def _calculate_commercial_score(self, metrics: KeywordMetrics) -> float:
        """计算商业价值评分"""
        score = 0.0
        
        # CPC评分 (权重40%)
        if metrics.cpc is not None:
            if metrics.cpc >= self.benchmarks["high_cpc"]:
                cpc_score = 1.0
            elif metrics.cpc >= self.benchmarks["medium_cpc"]:
                cpc_score = 0.5 + 0.5 * (metrics.cpc / self.benchmarks["high_cpc"])
            else:
                cpc_score = 0.5 * (metrics.cpc / self.benchmarks["medium_cpc"])
            score += 0.4 * cpc_score
        
        # 搜索意图评分 (权重35%)
        if metrics.search_intent:
            intent_scores = {
                SearchIntent.TRANSACTIONAL.value: 1.0,
                SearchIntent.COMMERCIAL.value: 0.8,
                SearchIntent.INFORMATIONAL.value: 0.4,
                SearchIntent.NAVIGATIONAL.value: 0.2
            }
            score += 0.35 * intent_scores.get(metrics.search_intent, 0.4)
        
        # 竞争度评分 (权重25%, 竞争度适中最好)
        if metrics.competition is not None:
            if 0.3 <= metrics.competition <= 0.7:
                competition_score = 1.0  # 适中竞争度最好
            elif metrics.competition < 0.3:
                competition_score = 0.6  # 竞争度太低可能意味着市场小
            else:
                competition_score = max(0.2, 1.0 - (metrics.competition - 0.7) * 2)
            score += 0.25 * competition_score
        
        return min(1.0, score)
    
    def _calculate_opportunity_score(self, metrics: KeywordMetrics) -> float:
        """计算机会评分"""
        score = 0.0
        
        # SERP质量评分 (权重60%)
        if metrics.serp_quality_score is not None:
            score += 0.6 * metrics.serp_quality_score
        
        # 趋势评分 (权重40%)
        if metrics.trend_score is not None:
            score += 0.4 * metrics.trend_score
        
        # 零点击率惩罚
        if metrics.zero_click_rate is not None and metrics.zero_click_rate > 0.5:
            penalty = min(0.3, (metrics.zero_click_rate - 0.5) * 0.6)
            score = max(0.0, score - penalty)
        
        return min(1.0, score)
    
    def _calculate_feasibility_score(self, metrics: KeywordMetrics) -> float:
        """计算可行性评分 (基于工具实现复杂度)"""
        if metrics.tool_complexity is None:
            return 0.5  # 默认中等分数
        
        # 复杂度越低，可行性越高
        complexity_scores = {
            ToolComplexity.VERY_SIMPLE.value: 1.0,
            ToolComplexity.SIMPLE.value: 0.8,
            ToolComplexity.MODERATE.value: 0.6,
            ToolComplexity.COMPLEX.value: 0.4,
            ToolComplexity.VERY_COMPLEX.value: 0.2
        }
        
        base_score = complexity_scores.get(metrics.tool_complexity, 0.5)
        
        # 根据工具类别调整
        if metrics.tool_category:
            category_multipliers = {
                "converter": 1.1,      # 转换器相对容易
                "calculator": 1.0,     # 计算器中等难度
                "generator": 0.9,      # 生成器稍复杂
                "editor": 0.7,         # 编辑器较复杂
                "analyzer": 0.8        # 分析器较复杂
            }
            multiplier = category_multipliers.get(metrics.tool_category.lower(), 1.0)
            base_score *= multiplier
        
        return min(1.0, base_score)
    
    def _apply_adjustments(self, base_score: float, metrics: KeywordMetrics) -> float:
        """应用调整因子"""
        adjusted_score = base_score
        
        # 长尾关键词奖励 (3个词以上)
        word_count = len(metrics.keyword.split())
        if word_count >= 3:
            long_tail_bonus = min(0.1, (word_count - 2) * 0.03)
            adjusted_score += long_tail_bonus
        
        # 品牌词惩罚
        brand_indicators = ["google", "facebook", "microsoft", "adobe", "apple"]
        if any(brand in metrics.keyword.lower() for brand in brand_indicators):
            adjusted_score *= 0.7
        
        # 高价值关键词模式奖励
        high_value_patterns = ["free", "online", "tool", "generator", "converter", "calculator"]
        pattern_matches = sum(1 for pattern in high_value_patterns if pattern in metrics.keyword.lower())
        if pattern_matches >= 2:
            adjusted_score += 0.05
        
        return adjusted_score
    
    def _get_score_grade(self, score: float) -> str:
        """获取评分等级"""
        if score >= 0.8:
            return "A"
        elif score >= 0.65:
            return "B"
        elif score >= 0.5:
            return "C"
        elif score >= 0.35:
            return "D"
        else:
            return "F"
    
    def _get_recommendation(self, score: float, metrics: KeywordMetrics) -> str:
        """获取建议"""
        if score >= 0.8:
            return "强烈推荐：高潜力关键词，建议优先开发"
        elif score >= 0.65:
            return "推荐：良好的潜力，值得考虑开发"
        elif score >= 0.5:
            return "考虑：中等潜力，需要进一步评估"
        elif score >= 0.35:
            return "谨慎：潜力有限，建议观察市场变化"
        else:
            return "不推荐：潜力较低，不建议开发"
    
    def batch_score_keywords(self, keywords_metrics: List[KeywordMetrics]) -> List[Dict[str, Any]]:
        """批量评分关键词"""
        results = []
        
        for metrics in keywords_metrics:
            try:
                score_result = self.calculate_potential_score(metrics)
                score_result["keyword"] = metrics.keyword
                results.append(score_result)
            except Exception as e:
                logger.error(f"Error scoring keyword '{metrics.keyword}': {str(e)}")
                results.append({
                    "keyword": metrics.keyword,
                    "potential_score": 0.0,
                    "grade": "F",
                    "recommendation": "评分失败"
                })
        
        # 按分数排序
        results.sort(key=lambda x: x["potential_score"], reverse=True)
        
        return results
    
    def get_top_opportunities(self, keywords_metrics: List[KeywordMetrics], limit: int = 20) -> List[Dict[str, Any]]:
        """获取最佳机会关键词"""
        scored_keywords = self.batch_score_keywords(keywords_metrics)
        
        # 筛选高分关键词
        opportunities = [
            kw for kw in scored_keywords 
            if kw["potential_score"] >= 0.6 and kw["grade"] in ["A", "B"]
        ]
        
        return opportunities[:limit]
    
    def analyze_scoring_factors(self, metrics: KeywordMetrics) -> Dict[str, Any]:
        """分析评分因子，提供详细解释"""
        
        analysis = {
            "keyword": metrics.keyword,
            "factors": {},
            "improvements": [],
            "risks": []
        }
        
        # 分析各个因子
        if metrics.search_volume is not None:
            if metrics.search_volume < 100:
                analysis["factors"]["volume"] = "搜索量过低"
                analysis["improvements"].append("寻找搜索量更高的相关关键词")
            elif metrics.search_volume > 50000:
                analysis["factors"]["volume"] = "搜索量很高，竞争可能激烈"
                analysis["risks"].append("高搜索量意味着高竞争")
        
        if metrics.keyword_difficulty is not None:
            if metrics.keyword_difficulty > 80:
                analysis["factors"]["difficulty"] = "关键词难度很高"
                analysis["risks"].append("SEO排名难度极大")
            elif metrics.keyword_difficulty < 30:
                analysis["factors"]["difficulty"] = "关键词难度较低，机会较好"
        
        if metrics.cpc is not None:
            if metrics.cpc > 3.0:
                analysis["factors"]["commercial"] = "商业价值高"
            elif metrics.cpc < 0.5:
                analysis["factors"]["commercial"] = "商业价值较低"
                analysis["improvements"].append("考虑相关的高价值关键词")
        
        return analysis


class ScenarioBasedScoring:
    """基于场景的评分策略"""
    
    @staticmethod
    def get_aggressive_weights() -> ScoringWeights:
        """激进策略：重视搜索量和商业价值"""
        return ScoringWeights(
            volume=0.35,
            difficulty=0.15,
            commercial=0.30,
            opportunity=0.10,
            feasibility=0.10
        )
    
    @staticmethod
    def get_conservative_weights() -> ScoringWeights:
        """保守策略：重视可行性和低难度"""
        return ScoringWeights(
            volume=0.15,
            difficulty=0.30,
            commercial=0.15,
            opportunity=0.15,
            feasibility=0.25
        )
    
    @staticmethod
    def get_balanced_weights() -> ScoringWeights:
        """平衡策略：均衡各项指标"""
        return ScoringWeights(
            volume=0.20,
            difficulty=0.20,
            commercial=0.20,
            opportunity=0.20,
            feasibility=0.20
        )