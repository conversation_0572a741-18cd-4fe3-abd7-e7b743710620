"""Celery应用配置"""

from celery import Celery
from app.config import settings
import os

# 创建Celery实例
celery_app = Celery(
    "keywork",
    broker=os.getenv("CELERY_BROKER_URL", "redis://localhost:6379/0"),
    backend=os.getenv("CELERY_RESULT_BACKEND", "redis://localhost:6379/0"),
    include=["app.tasks.keyword_tasks", "app.tasks.website_tasks"]
)

# Celery配置
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30分钟任务超时
    task_soft_time_limit=25 * 60,  # 25分钟软超时
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# 任务路由配置
celery_app.conf.task_routes = {
    "app.tasks.keyword_tasks.*": {"queue": "keyword_queue"},
    "app.tasks.website_tasks.*": {"queue": "website_queue"},
}

# 定期任务配置
celery_app.conf.beat_schedule = {
    "update_keyword_data": {
        "task": "app.tasks.keyword_tasks.update_stale_keywords",
        "schedule": 3600.0,  # 每小时执行一次
    },
    "cleanup_failed_tasks": {
        "task": "app.tasks.maintenance_tasks.cleanup_failed_tasks",
        "schedule": 24 * 3600.0,  # 每天执行一次
    },
}