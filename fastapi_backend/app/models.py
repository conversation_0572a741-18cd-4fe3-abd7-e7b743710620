from fastapi_users.db import SQLAlchemyBaseUserTableUUID
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy import Column, String, Integer, ForeignKey, Float, DateTime, Boolean, Text, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from uuid import uuid4
from datetime import datetime


class Base(DeclarativeBase):
    pass


class User(SQLAlchemyBaseUserTableUUID, Base):
    items = relationship("Item", back_populates="user", cascade="all, delete-orphan")


class Item(Base):
    __tablename__ = "items"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    quantity = Column(Integer, nullable=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("user.id"), nullable=False)

    user = relationship("User", back_populates="items")


class Keyword(Base):
    """关键词表 - 存储所有关键词及其基础数据"""
    __tablename__ = "keywords"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    keyword = Column(String(255), nullable=False, index=True)
    language = Column(String(10), nullable=False, default="en")
    country = Column(String(10), nullable=False, default="us")
    
    # SEO指标
    search_volume = Column(Integer, nullable=True)  # 月搜索量
    keyword_difficulty = Column(Float, nullable=True)  # 关键词难度(0-100)
    cpc = Column(Float, nullable=True)  # 每次点击成本
    competition = Column(Float, nullable=True)  # 竞争度
    search_intent = Column(String(50), nullable=True)  # 搜索意图: informational, commercial, transactional
    zero_click_rate = Column(Float, nullable=True)  # 零点击率
    
    # 趋势数据
    trend_score = Column(Float, nullable=True)  # 趋势评分
    trend_data = Column(JSON, nullable=True)  # 趋势数据详情
    
    # SERP分析
    serp_quality_score = Column(Float, nullable=True)  # SERP质量分数
    serp_analysis = Column(JSON, nullable=True)  # SERP分析详情
    
    # 工具复杂度评估
    tool_complexity = Column(Integer, nullable=True)  # 工具复杂度(1-5)
    tool_category = Column(String(100), nullable=True)  # 工具分类
    
    # 潜力评分
    potential_score = Column(Float, nullable=True)  # 综合潜力评分
    
    # 状态管理
    status = Column(String(20), nullable=False, default="raw")  # raw, cleaned, enriched, evaluated, approved, rejected
    is_approved = Column(Boolean, nullable=False, default=False)
    
    # 时间戳
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_checked_at = Column(DateTime, nullable=True)
    next_check_at = Column(DateTime, nullable=True)
    
    # 关联关系
    websites = relationship("Website", back_populates="keyword")
    tasks = relationship("KeywordTask", back_populates="keyword")


class Website(Base):
    """网站表 - 存储生成的网站信息"""
    __tablename__ = "websites"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    keyword_id = Column(UUID(as_uuid=True), ForeignKey("keywords.id"), nullable=False)
    
    # 网站基本信息
    domain = Column(String(255), nullable=True)
    subdomain = Column(String(255), nullable=True)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # 内容数据
    content_data = Column(JSON, nullable=True)  # 生成的内容数据
    seo_metadata = Column(JSON, nullable=True)  # SEO元数据
    
    # 技术信息
    template_id = Column(String(100), nullable=True)  # 使用的模板ID
    github_repo = Column(String(255), nullable=True)  # GitHub仓库地址
    deployment_url = Column(String(255), nullable=True)  # 部署地址
    
    # 状态
    status = Column(String(20), nullable=False, default="pending")  # pending, generating, building, deployed, failed
    build_log = Column(Text, nullable=True)  # 构建日志
    
    # 性能指标
    lighthouse_score = Column(JSON, nullable=True)  # Lighthouse评分
    page_speed = Column(Float, nullable=True)  # 页面速度
    
    # 时间戳
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    deployed_at = Column(DateTime, nullable=True)
    
    # 关联关系
    keyword = relationship("Keyword", back_populates="websites")
    tasks = relationship("WebsiteTask", back_populates="website")


class KeywordTask(Base):
    """关键词处理任务表"""
    __tablename__ = "keyword_tasks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    keyword_id = Column(UUID(as_uuid=True), ForeignKey("keywords.id"), nullable=False)
    
    # 任务信息
    task_type = Column(String(50), nullable=False)  # scraping, analysis, evaluation
    task_status = Column(String(20), nullable=False, default="pending")  # pending, running, completed, failed
    celery_task_id = Column(String(255), nullable=True)  # Celery任务ID
    
    # 任务配置
    task_config = Column(JSON, nullable=True)  # 任务配置参数
    
    # 结果数据
    result_data = Column(JSON, nullable=True)  # 任务结果
    error_message = Column(Text, nullable=True)  # 错误信息
    
    # 时间戳
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # 关联关系
    keyword = relationship("Keyword", back_populates="tasks")


class WebsiteTask(Base):
    """网站生成任务表"""
    __tablename__ = "website_tasks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    website_id = Column(UUID(as_uuid=True), ForeignKey("websites.id"), nullable=False)
    
    # 任务信息
    task_type = Column(String(50), nullable=False)  # content_generation, code_generation, deployment
    task_status = Column(String(20), nullable=False, default="pending")  # pending, running, completed, failed
    celery_task_id = Column(String(255), nullable=True)  # Celery任务ID
    
    # 任务配置
    task_config = Column(JSON, nullable=True)  # 任务配置参数
    
    # 结果数据
    result_data = Column(JSON, nullable=True)  # 任务结果
    error_message = Column(Text, nullable=True)  # 错误信息
    
    # 时间戳
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # 关联关系
    website = relationship("Website", back_populates="tasks")


class SeedKeyword(Base):
    """种子关键词表 - 用于关键词扩展的基础词根"""
    __tablename__ = "seed_keywords"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # 种子词信息
    root_word = Column(String(100), nullable=False)  # 词根
    category = Column(String(100), nullable=True)  # 分类
    priority = Column(Integer, nullable=False, default=1)  # 优先级
    
    # 扩展配置
    modifiers = Column(JSON, nullable=True)  # 修饰词列表
    language = Column(String(10), nullable=False, default="en")
    country = Column(String(10), nullable=False, default="us")
    
    # 状态
    is_active = Column(Boolean, nullable=False, default=True)
    last_expanded_at = Column(DateTime, nullable=True)
    
    # 时间戳
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
