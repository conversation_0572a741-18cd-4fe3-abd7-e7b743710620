"""Pydantic schemas for keyword analysis system"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from uuid import UUID


class KeywordBase(BaseModel):
    keyword: str = Field(..., min_length=1, max_length=255, description="关键词")
    language: str = Field(default="en", max_length=10, description="语言代码")
    country: str = Field(default="us", max_length=10, description="国家代码")


class KeywordCreate(KeywordBase):
    search_volume: Optional[int] = Field(None, ge=0, description="月搜索量")
    keyword_difficulty: Optional[float] = Field(None, ge=0, le=100, description="关键词难度")
    cpc: Optional[float] = Field(None, ge=0, description="每次点击成本")
    competition: Optional[float] = Field(None, ge=0, le=1, description="竞争度")
    search_intent: Optional[str] = Field(None, max_length=50, description="搜索意图")
    zero_click_rate: Optional[float] = Field(None, ge=0, le=1, description="零点击率")
    tool_complexity: Optional[int] = Field(None, ge=1, le=5, description="工具复杂度")
    tool_category: Optional[str] = Field(None, max_length=100, description="工具分类")


class KeywordUpdate(BaseModel):
    search_volume: Optional[int] = Field(None, ge=0)
    keyword_difficulty: Optional[float] = Field(None, ge=0, le=100)
    cpc: Optional[float] = Field(None, ge=0)
    competition: Optional[float] = Field(None, ge=0, le=1)
    search_intent: Optional[str] = Field(None, max_length=50)
    zero_click_rate: Optional[float] = Field(None, ge=0, le=1)
    trend_score: Optional[float] = Field(None)
    trend_data: Optional[Dict[str, Any]] = None
    serp_quality_score: Optional[float] = Field(None, ge=0, le=1)
    serp_analysis: Optional[Dict[str, Any]] = None
    tool_complexity: Optional[int] = Field(None, ge=1, le=5)
    tool_category: Optional[str] = Field(None, max_length=100)
    potential_score: Optional[float] = Field(None)
    status: Optional[str] = Field(None, max_length=20)
    is_approved: Optional[bool] = None


class KeywordResponse(KeywordBase):
    id: UUID
    search_volume: Optional[int] = None
    keyword_difficulty: Optional[float] = None
    cpc: Optional[float] = None
    competition: Optional[float] = None
    search_intent: Optional[str] = None
    zero_click_rate: Optional[float] = None
    trend_score: Optional[float] = None
    trend_data: Optional[Dict[str, Any]] = None
    serp_quality_score: Optional[float] = None
    serp_analysis: Optional[Dict[str, Any]] = None
    tool_complexity: Optional[int] = None
    tool_category: Optional[str] = None
    potential_score: Optional[float] = None
    status: str
    is_approved: bool
    created_at: datetime
    updated_at: datetime
    last_checked_at: Optional[datetime] = None
    next_check_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class KeywordListResponse(BaseModel):
    keywords: List[KeywordResponse]
    total: int
    page: int
    size: int
    total_pages: int


class WebsiteBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=255, description="网站标题")
    description: Optional[str] = Field(None, description="网站描述")


class WebsiteCreate(WebsiteBase):
    keyword_id: UUID = Field(..., description="关联的关键词ID")
    domain: Optional[str] = Field(None, max_length=255, description="域名")
    subdomain: Optional[str] = Field(None, max_length=255, description="子域名")
    template_id: Optional[str] = Field(None, max_length=100, description="模板ID")


class WebsiteUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    domain: Optional[str] = Field(None, max_length=255)
    subdomain: Optional[str] = Field(None, max_length=255)
    content_data: Optional[Dict[str, Any]] = None
    seo_metadata: Optional[Dict[str, Any]] = None
    template_id: Optional[str] = Field(None, max_length=100)
    github_repo: Optional[str] = Field(None, max_length=255)
    deployment_url: Optional[str] = Field(None, max_length=255)
    status: Optional[str] = Field(None, max_length=20)
    build_log: Optional[str] = None
    lighthouse_score: Optional[Dict[str, Any]] = None
    page_speed: Optional[float] = None


class WebsiteResponse(WebsiteBase):
    id: UUID
    keyword_id: UUID
    domain: Optional[str] = None
    subdomain: Optional[str] = None
    content_data: Optional[Dict[str, Any]] = None
    seo_metadata: Optional[Dict[str, Any]] = None
    template_id: Optional[str] = None
    github_repo: Optional[str] = None
    deployment_url: Optional[str] = None
    status: str
    build_log: Optional[str] = None
    lighthouse_score: Optional[Dict[str, Any]] = None
    page_speed: Optional[float] = None
    created_at: datetime
    updated_at: datetime
    deployed_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class SeedKeywordBase(BaseModel):
    root_word: str = Field(..., min_length=1, max_length=100, description="词根")
    category: Optional[str] = Field(None, max_length=100, description="分类")
    priority: int = Field(default=1, ge=1, le=10, description="优先级")
    language: str = Field(default="en", max_length=10, description="语言代码")
    country: str = Field(default="us", max_length=10, description="国家代码")


class SeedKeywordCreate(SeedKeywordBase):
    modifiers: Optional[List[str]] = Field(None, description="修饰词列表")
    is_active: bool = Field(default=True, description="是否激活")


class SeedKeywordUpdate(BaseModel):
    root_word: Optional[str] = Field(None, min_length=1, max_length=100)
    category: Optional[str] = Field(None, max_length=100)
    priority: Optional[int] = Field(None, ge=1, le=10)
    modifiers: Optional[List[str]] = None
    language: Optional[str] = Field(None, max_length=10)
    country: Optional[str] = Field(None, max_length=10)
    is_active: Optional[bool] = None


class SeedKeywordResponse(SeedKeywordBase):
    id: UUID
    modifiers: Optional[List[str]] = None
    is_active: bool
    last_expanded_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class TaskBase(BaseModel):
    task_type: str = Field(..., max_length=50, description="任务类型")
    task_config: Optional[Dict[str, Any]] = Field(None, description="任务配置")


class KeywordTaskCreate(TaskBase):
    keyword_id: UUID = Field(..., description="关联的关键词ID")


class WebsiteTaskCreate(TaskBase):
    website_id: UUID = Field(..., description="关联的网站ID")


class TaskResponse(TaskBase):
    id: UUID
    task_status: str
    celery_task_id: Optional[str] = None
    result_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class KeywordTaskResponse(TaskResponse):
    keyword_id: UUID


class WebsiteTaskResponse(TaskResponse):
    website_id: UUID


class PotentialScoreRequest(BaseModel):
    """潜力评分计算请求"""
    volume_weight: float = Field(default=0.3, ge=0, le=1, description="搜索量权重")
    difficulty_weight: float = Field(default=0.3, ge=0, le=1, description="难度权重")
    cpc_weight: float = Field(default=0.2, ge=0, le=1, description="CPC权重")
    trend_weight: float = Field(default=0.1, ge=0, le=1, description="趋势权重")
    serp_weight: float = Field(default=0.1, ge=0, le=1, description="SERP质量权重")


class KeywordAnalysisRequest(BaseModel):
    """关键词分析请求"""
    keywords: List[str] = Field(..., min_items=1, max_items=100, description="关键词列表")
    language: str = Field(default="en", max_length=10, description="语言代码")
    country: str = Field(default="us", max_length=10, description="国家代码")
    include_serp: bool = Field(default=True, description="是否包含SERP分析")
    include_trends: bool = Field(default=True, description="是否包含趋势分析")