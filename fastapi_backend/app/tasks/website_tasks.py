"""网站生成和部署异步任务"""

from celery import current_task
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from datetime import datetime, timedelta
from typing import Dict, Any
import logging
import asyncio
import os

from app.services.celery_app import celery_app
from app.models import Website, WebsiteTask, Keyword
from app.services.website_generator import WebsiteGenerator, WebsiteConfig
from app.services.content_ai import ContentAI, WebsiteContentGenerator
from app.services.deployment import DeploymentService, DeploymentConfig
from app.config import settings

logger = logging.getLogger(__name__)

# 创建数据库会话
engine = create_engine(settings.database_url)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 初始化服务
website_generator = WebsiteGenerator()
deployment_service = DeploymentService()

# AI内容生成（如果配置了OpenAI API密钥）
openai_api_key = os.getenv("OPENAI_API_KEY")
if openai_api_key:
    content_ai = ContentAI(openai_api_key)
    ai_content_generator = WebsiteContentGenerator(content_ai)
else:
    content_ai = None
    ai_content_generator = None


@celery_app.task(bind=True, name="generate_website_complete")
def generate_website_complete(self, website_id: str) -> Dict[str, Any]:
    """完整的网站生成任务"""
    
    task_id = self.request.id
    db = SessionLocal()
    
    try:
        # 获取网站记录
        website = db.query(Website).filter(Website.id == website_id).first()
        if not website:
            return {"error": "Website not found"}
        
        keyword = website.keyword
        
        # 创建任务记录
        task = WebsiteTask(
            website_id=website.id,
            task_type="complete_generation",
            celery_task_id=task_id,
            task_status="running"
        )
        db.add(task)
        db.commit()
        
        # 更新进度
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 1, "total": 5, "status": "开始生成网站"}
        )
        
        # 1. 内容生成阶段
        website.status = "generating"
        db.commit()
        
        if ai_content_generator:
            current_task.update_state(
                state="PROGRESS",
                meta={"current": 2, "total": 5, "status": "生成AI内容"}
            )
            
            # 异步生成内容
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                ai_content = loop.run_until_complete(
                    ai_content_generator.generate_complete_website_content(
                        keyword=keyword.keyword,
                        tool_type=keyword.tool_category or "tool",
                        language=keyword.language
                    )
                )
                
                website.content_data = ai_content
                db.commit()
                
            except Exception as e:
                logger.warning(f"AI content generation failed: {str(e)}, using default content")
            finally:
                loop.close()
        
        # 2. 网站结构生成
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 3, "total": 5, "status": "生成网站代码"}
        )
        
        config = WebsiteConfig(
            keyword=keyword.keyword,
            title=website.title,
            description=website.description or f"Free online {keyword.keyword.lower()} tool",
            tool_type=keyword.tool_category or "tool",
            template_id=website.template_id or "",
            language=keyword.language,
            domain=website.domain
        )
        
        # 生成网站
        generation_result = website_generator.generate_website(config)
        
        if not generation_result["success"]:
            website.status = "failed"
            website.build_log = generation_result.get("error", "Unknown error")
            task.task_status = "failed"
            task.error_message = generation_result.get("error")
            db.commit()
            return {"error": generation_result.get("error")}
        
        # 更新网站信息
        website.github_repo = generation_result.get("project_path")
        website.build_log = f"Generated successfully. Files: {len(generation_result.get('files_generated', []))}"
        website.status = "built"
        db.commit()
        
        # 3. 部署阶段
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 4, "total": 5, "status": "部署网站"}
        )
        
        if deployment_service.enabled:
            # 生成部署配置
            deploy_config = deployment_service.generate_deployment_config(
                project_name=f"{keyword.keyword.replace(' ', '-').lower()}-tool",
                project_path=generation_result["project_path"],
                domain=website.domain
            )
            
            # 执行部署
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                deployment_result = loop.run_until_complete(
                    deployment_service.deploy_website(deploy_config)
                )
                
                if deployment_result.success:
                    website.status = "deployed"
                    website.deployment_url = deployment_result.vercel_deployment_url
                    website.github_repo = deployment_result.github_repo_url
                    website.deployed_at = datetime.utcnow()
                    
                    # 合并构建日志和部署日志
                    all_logs = [website.build_log] + (deployment_result.logs or [])
                    website.build_log = '\n'.join(all_logs)
                else:
                    website.status = "deploy_failed"
                    website.build_log += f"\nDeployment failed: {deployment_result.error_message}"
                
            except Exception as e:
                logger.error(f"Deployment failed: {str(e)}")
                website.status = "deploy_failed"
                website.build_log += f"\nDeployment error: {str(e)}"
            finally:
                loop.close()
        else:
            logger.warning("Deployment service not configured, skipping deployment")
        
        # 4. 完成
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 5, "total": 5, "status": "完成"}
        )
        
        task.task_status = "completed"
        task.completed_at = datetime.utcnow()
        task.result_data = {
            "website_status": website.status,
            "deployment_url": website.deployment_url,
            "github_repo": website.github_repo
        }
        
        db.commit()
        
        return {
            "success": True,
            "website_id": website_id,
            "status": website.status,
            "deployment_url": website.deployment_url,
            "github_repo": website.github_repo
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"Complete website generation failed for {website_id}: {str(e)}")
        
        # 更新失败状态
        website = db.query(Website).filter(Website.id == website_id).first()
        if website:
            website.status = "failed"
            website.build_log = str(e)
        
        task = db.query(WebsiteTask).filter(WebsiteTask.celery_task_id == task_id).first()
        if task:
            task.task_status = "failed"
            task.error_message = str(e)
            task.completed_at = datetime.utcnow()
        
        db.commit()
        return {"error": str(e)}
    
    finally:
        db.close()


@celery_app.task(name="batch_generate_websites")
def batch_generate_websites(keyword_ids: list) -> Dict[str, Any]:
    """批量生成网站任务"""
    
    db = SessionLocal()
    
    try:
        # 为每个关键词创建网站记录
        created_websites = []
        
        for keyword_id in keyword_ids:
            keyword = db.query(Keyword).filter(Keyword.id == keyword_id).first()
            if not keyword:
                continue
            
            # 检查是否已有网站
            existing = db.query(Website).filter(Website.keyword_id == keyword.id).first()
            if existing:
                continue
            
            # 创建网站记录
            website = Website(
                keyword_id=keyword.id,
                title=f"{keyword.keyword.title()} Tool",
                description=f"Free online {keyword.keyword.lower()} tool",
                status="pending"
            )
            db.add(website)
            db.flush()
            
            created_websites.append(website)
            
            # 启动完整生成任务
            generate_website_complete.delay(str(website.id))
        
        db.commit()
        
        return {
            "success": True,
            "created_count": len(created_websites),
            "websites": [
                {
                    "id": str(w.id),
                    "keyword": w.keyword.keyword,
                    "status": w.status
                }
                for w in created_websites
            ]
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"Batch website generation failed: {str(e)}")
        return {"error": str(e)}
    
    finally:
        db.close()


@celery_app.task(name="redeploy_website")
def redeploy_website(website_id: str) -> Dict[str, Any]:
    """重新部署网站任务"""
    
    db = SessionLocal()
    
    try:
        website = db.query(Website).filter(Website.id == website_id).first()
        if not website:
            return {"error": "Website not found"}
        
        if not website.github_repo:
            return {"error": "No GitHub repository found for this website"}
        
        # 更新状态
        website.status = "deploying"
        db.commit()
        
        if deployment_service.enabled:
            # 触发重新部署
            deploy_config = deployment_service.generate_deployment_config(
                project_name=website.title.replace(" ", "-").lower(),
                project_path=website.github_repo,
                domain=website.domain
            )
            
            # 执行部署
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                deployment_result = loop.run_until_complete(
                    deployment_service.deploy_website(deploy_config)
                )
                
                if deployment_result.success:
                    website.status = "deployed"
                    website.deployment_url = deployment_result.vercel_deployment_url
                    website.deployed_at = datetime.utcnow()
                else:
                    website.status = "deploy_failed"
                    website.build_log += f"\nRedeployment failed: {deployment_result.error_message}"
                
            except Exception as e:
                logger.error(f"Redeployment failed: {str(e)}")
                website.status = "deploy_failed"
                website.build_log += f"\nRedeployment error: {str(e)}"
            finally:
                loop.close()
        
        db.commit()
        
        return {
            "success": True,
            "website_id": website_id,
            "status": website.status,
            "deployment_url": website.deployment_url
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"Website redeployment failed for {website_id}: {str(e)}")
        return {"error": str(e)}
    
    finally:
        db.close()


@celery_app.task(name="monitor_deployments")
def monitor_deployments() -> Dict[str, Any]:
    """监控部署状态任务"""
    
    db = SessionLocal()
    
    try:
        # 查找处于部署中状态的网站
        deploying_websites = db.query(Website).filter(
            Website.status.in_(["deploying", "deployed"])
        ).all()
        
        checked_count = 0
        updated_count = 0
        
        for website in deploying_websites:
            if not website.deployment_url:
                continue
            
            checked_count += 1
            
            # 检查部署状态
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                status = loop.run_until_complete(
                    deployment_service.check_deployment_status(website.deployment_url)
                )
                
                if status["accessible"] and website.status != "live":
                    website.status = "live"
                    updated_count += 1
                elif not status["accessible"] and website.status == "live":
                    website.status = "error"
                    updated_count += 1
                
            except Exception as e:
                logger.error(f"Failed to check deployment status for {website.id}: {str(e)}")
            finally:
                loop.close()
        
        db.commit()
        
        return {
            "success": True,
            "checked_count": checked_count,
            "updated_count": updated_count
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"Deployment monitoring failed: {str(e)}")
        return {"error": str(e)}
    
    finally:
        db.close()


@celery_app.task(name="cleanup_failed_websites")
def cleanup_failed_websites() -> Dict[str, Any]:
    """清理失败的网站记录"""
    
    db = SessionLocal()
    
    try:
        # 查找超过一定时间的失败网站
        cutoff_date = datetime.utcnow() - timedelta(days=7)
        
        failed_websites = db.query(Website).filter(
            Website.status.in_(["failed", "deploy_failed"]),
            Website.created_at < cutoff_date
        ).all()
        
        cleanup_count = 0
        
        for website in failed_websites:
            # 可以选择删除或归档
            # 这里我们只是标记为已清理
            website.status = "archived"
            cleanup_count += 1
        
        db.commit()
        
        return {
            "success": True,
            "cleanup_count": cleanup_count
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"Website cleanup failed: {str(e)}")
        return {"error": str(e)}
    
    finally:
        db.close()


@celery_app.task(name="generate_performance_report")
def generate_performance_report() -> Dict[str, Any]:
    """生成性能报告任务"""
    
    db = SessionLocal()
    
    try:
        # 统计各种状态的网站数量
        from sqlalchemy import func
        
        status_counts = db.query(
            Website.status,
            func.count(Website.id).label("count")
        ).group_by(Website.status).all()
        
        # 统计最近7天的生成情况
        week_ago = datetime.utcnow() - timedelta(days=7)
        recent_count = db.query(Website).filter(
            Website.created_at >= week_ago
        ).count()
        
        # 统计成功部署率
        total_websites = db.query(Website).count()
        deployed_websites = db.query(Website).filter(
            Website.status.in_(["deployed", "live"])
        ).count()
        
        success_rate = (deployed_websites / total_websites * 100) if total_websites > 0 else 0
        
        report = {
            "generated_at": datetime.utcnow().isoformat(),
            "total_websites": total_websites,
            "recent_week_count": recent_count,
            "success_rate": round(success_rate, 2),
            "status_breakdown": {status: count for status, count in status_counts},
            "deployed_count": deployed_websites
        }
        
        return {
            "success": True,
            "report": report
        }
        
    except Exception as e:
        logger.error(f"Performance report generation failed: {str(e)}")
        return {"error": str(e)}
    
    finally:
        db.close()