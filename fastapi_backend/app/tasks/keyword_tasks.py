"""关键词处理异步任务"""

from celery import current_task
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from datetime import datetime, timedelta
from typing import List, Dict, Any
import logging
import asyncio

from app.services.celery_app import celery_app
from app.models import Keyword, KeywordTask, SeedKeyword
from app.services.keyword_analyzer import KeywordAnalyzer
from app.services.scoring_engine import ScoringEngine, KeywordMetrics
from app.config import settings

logger = logging.getLogger(__name__)

# 创建数据库会话
engine = create_engine(settings.database_url)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 初始化服务
keyword_analyzer = KeywordAnalyzer()
scoring_engine = ScoringEngine()


@celery_app.task(bind=True, name="analyze_keywords_batch")
def analyze_keywords_batch(self, keyword_ids: List[str]) -> Dict[str, Any]:
    """批量分析关键词任务"""
    
    task_id = self.request.id
    db = SessionLocal()
    
    try:
        # 更新任务状态
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 0, "total": len(keyword_ids), "status": "开始分析关键词"}
        )
        
        # 获取关键词
        keywords = db.query(Keyword).filter(Keyword.id.in_(keyword_ids)).all()
        if not keywords:
            return {"error": "未找到关键词"}
        
        # 分析关键词
        results = []
        for i, keyword in enumerate(keywords):
            try:
                # 更新进度
                current_task.update_state(
                    state="PROGRESS",
                    meta={
                        "current": i + 1,
                        "total": len(keywords),
                        "status": f"分析关键词: {keyword.keyword}"
                    }
                )
                
                # 执行分析 (需要在同步任务中运行异步代码)
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    metrics = loop.run_until_complete(
                        keyword_analyzer.analyze_keyword(
                            keyword.keyword,
                            keyword.language,
                            keyword.country
                        )
                    )
                finally:
                    loop.close()
                
                # 更新关键词数据
                keyword.search_volume = metrics.search_volume
                keyword.keyword_difficulty = metrics.keyword_difficulty
                keyword.cpc = metrics.cpc
                keyword.competition = metrics.competition
                keyword.search_intent = metrics.search_intent
                keyword.zero_click_rate = metrics.zero_click_rate
                keyword.trend_score = metrics.trend_score
                keyword.trend_data = metrics.trend_data
                keyword.serp_quality_score = metrics.serp_quality_score
                keyword.serp_analysis = metrics.serp_analysis
                keyword.status = "enriched"
                keyword.last_checked_at = datetime.utcnow()
                keyword.next_check_at = datetime.utcnow() + timedelta(days=7)
                
                # 计算潜力分数
                scoring_result = scoring_engine.calculate_potential_score(metrics)
                keyword.potential_score = scoring_result["potential_score"]
                
                results.append({
                    "keyword_id": str(keyword.id),
                    "keyword": keyword.keyword,
                    "potential_score": keyword.potential_score,
                    "grade": scoring_result["grade"]
                })
                
            except Exception as e:
                logger.error(f"Error analyzing keyword {keyword.keyword}: {str(e)}")
                keyword.status = "failed"
                results.append({
                    "keyword_id": str(keyword.id),
                    "keyword": keyword.keyword,
                    "error": str(e)
                })
        
        db.commit()
        
        return {
            "success": True,
            "analyzed": len(results),
            "results": results
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"Batch analysis task failed: {str(e)}")
        return {"error": str(e)}
    
    finally:
        db.close()


@celery_app.task(name="expand_seed_keywords")
def expand_seed_keywords(seed_keyword_id: str) -> Dict[str, Any]:
    """扩展种子关键词任务"""
    
    db = SessionLocal()
    
    try:
        # 获取种子关键词
        seed = db.query(SeedKeyword).filter(SeedKeyword.id == seed_keyword_id).first()
        if not seed:
            return {"error": "种子关键词不存在"}
        
        # 扩展关键词
        from app.services.keyword_analyzer import KeywordExpander
        expander = KeywordExpander()
        
        expanded_keywords = expander.expand_seed_keywords(
            [seed.root_word],
            seed.category or "tools"
        )
        
        # 如果有自定义修饰词
        if seed.modifiers:
            custom_expanded = expander.generate_keyword_combinations(
                [seed.root_word],
                seed.modifiers
            )
            expanded_keywords.extend(custom_expanded)
        
        # 去重
        expanded_keywords = list(set(expanded_keywords))
        
        # 创建关键词记录
        created_count = 0
        keyword_ids = []
        
        for kw in expanded_keywords:
            # 检查是否已存在
            existing = db.query(Keyword).filter(
                Keyword.keyword == kw,
                Keyword.language == seed.language,
                Keyword.country == seed.country
            ).first()
            
            if not existing:
                keyword = Keyword(
                    keyword=kw,
                    language=seed.language,
                    country=seed.country,
                    status="raw"
                )
                db.add(keyword)
                db.flush()  # 获取ID
                keyword_ids.append(str(keyword.id))
                created_count += 1
        
        # 更新种子词扩展时间
        seed.last_expanded_at = datetime.utcnow()
        db.commit()
        
        # 如果创建了新关键词，启动分析任务
        if keyword_ids:
            analyze_keywords_batch.delay(keyword_ids)
        
        return {
            "success": True,
            "total_expanded": len(expanded_keywords),
            "created_count": created_count,
            "keyword_ids": keyword_ids
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"Seed expansion task failed: {str(e)}")
        return {"error": str(e)}
    
    finally:
        db.close()


@celery_app.task(name="update_stale_keywords")
def update_stale_keywords() -> Dict[str, Any]:
    """更新过期的关键词数据"""
    
    db = SessionLocal()
    
    try:
        # 查找需要更新的关键词
        stale_cutoff = datetime.utcnow() - timedelta(days=7)
        stale_keywords = db.query(Keyword).filter(
            Keyword.next_check_at <= datetime.utcnow(),
            Keyword.status.in_(["enriched", "evaluated"])
        ).limit(100).all()  # 限制批次大小
        
        if not stale_keywords:
            return {"message": "没有需要更新的关键词"}
        
        # 获取关键词ID
        keyword_ids = [str(kw.id) for kw in stale_keywords]
        
        # 启动批量分析任务
        analyze_keywords_batch.delay(keyword_ids)
        
        return {
            "success": True,
            "updated_count": len(keyword_ids),
            "message": f"启动了 {len(keyword_ids)} 个关键词的更新任务"
        }
        
    except Exception as e:
        logger.error(f"Update stale keywords task failed: {str(e)}")
        return {"error": str(e)}
    
    finally:
        db.close()


@celery_app.task(name="calculate_keyword_scores")
def calculate_keyword_scores(keyword_ids: List[str], weights: Dict[str, float] = None) -> Dict[str, Any]:
    """重新计算关键词潜力分数"""
    
    db = SessionLocal()
    
    try:
        # 获取关键词
        keywords = db.query(Keyword).filter(Keyword.id.in_(keyword_ids)).all()
        
        # 准备权重
        from app.services.scoring_engine import ScoringWeights
        if weights:
            scoring_weights = ScoringWeights(**weights)
        else:
            scoring_weights = ScoringWeights()
        
        engine = ScoringEngine(scoring_weights)
        
        updated_count = 0
        results = []
        
        for keyword in keywords:
            # 构建指标对象
            metrics = KeywordMetrics(
                keyword=keyword.keyword,
                search_volume=keyword.search_volume,
                keyword_difficulty=keyword.keyword_difficulty,
                cpc=keyword.cpc,
                competition=keyword.competition,
                search_intent=keyword.search_intent,
                zero_click_rate=keyword.zero_click_rate,
                trend_score=keyword.trend_score,
                serp_quality_score=keyword.serp_quality_score,
                tool_complexity=keyword.tool_complexity
            )
            
            # 计算新分数
            scoring_result = engine.calculate_potential_score(metrics)
            
            # 更新关键词
            old_score = keyword.potential_score
            keyword.potential_score = scoring_result["potential_score"]
            
            results.append({
                "keyword_id": str(keyword.id),
                "keyword": keyword.keyword,
                "old_score": old_score,
                "new_score": keyword.potential_score,
                "grade": scoring_result["grade"]
            })
            
            updated_count += 1
        
        db.commit()
        
        return {
            "success": True,
            "updated_count": updated_count,
            "results": results
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"Score calculation task failed: {str(e)}")
        return {"error": str(e)}
    
    finally:
        db.close()


@celery_app.task(name="keyword_discovery")
def keyword_discovery(category: str, language: str = "en", country: str = "us") -> Dict[str, Any]:
    """关键词发现任务"""
    
    db = SessionLocal()
    
    try:
        # 基于类别的种子词
        category_seeds = {
            "converters": ["converter", "convert", "transform", "change"],
            "generators": ["generator", "maker", "creator", "builder"],
            "calculators": ["calculator", "calculate", "compute", "estimate"],
            "tools": ["tool", "utility", "helper", "assistant"],
            "editors": ["editor", "edit", "modify", "format"]
        }
        
        seeds = category_seeds.get(category, category_seeds["tools"])
        
        # 创建种子关键词
        for seed_word in seeds:
            existing_seed = db.query(SeedKeyword).filter(
                SeedKeyword.root_word == seed_word,
                SeedKeyword.language == language,
                SeedKeyword.country == country
            ).first()
            
            if not existing_seed:
                seed = SeedKeyword(
                    root_word=seed_word,
                    category=category,
                    language=language,
                    country=country,
                    priority=1
                )
                db.add(seed)
                db.flush()
                
                # 启动扩展任务
                expand_seed_keywords.delay(str(seed.id))
        
        db.commit()
        
        return {
            "success": True,
            "category": category,
            "message": f"启动了 {category} 类别的关键词发现任务"
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"Keyword discovery task failed: {str(e)}")
        return {"error": str(e)}
    
    finally:
        db.close()