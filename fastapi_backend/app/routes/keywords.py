"""关键词管理 API 路由"""

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, or_, desc, select
from typing import List, Optional
import logging
from uuid import UUID

from app.database import get_session
from app.models import Keyword, SeedKeyword, KeywordTask
from app.schemas_keyword import (
    KeywordCreate, KeywordUpdate, KeywordResponse, KeywordListResponse,
    SeedKeywordCreate, SeedKeywordUpdate, SeedKeywordResponse,
    KeywordAnalysisRequest, PotentialScoreRequest
)
from app.services.keyword_analyzer import KeywordAnalyzer, KeywordExpander

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/keywords", tags=["keywords"])

# 初始化关键词分析器
keyword_analyzer = KeywordAnalyzer()
keyword_expander = KeywordExpander()


@router.post("/", response_model=KeywordResponse)
async def create_keyword(
    keyword_data: KeywordCreate,
    db: AsyncSession = Depends(get_session)
):
    """创建新关键词"""
    
    # 检查关键词是否已存在
    result = await db.execute(select(Keyword).filter(
        and_(
            Keyword.keyword == keyword_data.keyword,
            Keyword.language == keyword_data.language,
            Keyword.country == keyword_data.country
        )
    ))
    existing = result.scalar_one_or_none()
    
    if existing:
        raise HTTPException(status_code=400, detail="关键词已存在")
    
    # 创建新关键词
    keyword = Keyword(**keyword_data.dict())
    db.add(keyword)
    await db.commit()
    await db.refresh(keyword)
    
    return keyword


@router.get("/", response_model=KeywordListResponse)
async def list_keywords(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    status: Optional[str] = Query(None, description="状态筛选"),
    language: Optional[str] = Query(None, description="语言筛选"),
    country: Optional[str] = Query(None, description="国家筛选"),
    search: Optional[str] = Query(None, description="关键词搜索"),
    min_volume: Optional[int] = Query(None, ge=0, description="最小搜索量"),
    max_difficulty: Optional[float] = Query(None, ge=0, le=100, description="最大难度"),
    approved_only: bool = Query(False, description="仅显示已批准的"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方向"),
    db: AsyncSession = Depends(get_session)
):
    """获取关键词列表"""
    
    # 构建查询
    query = select(Keyword)
    
    # 应用筛选条件
    if status:
        query = query.filter(Keyword.status == status)
    
    if language:
        query = query.filter(Keyword.language == language)
    
    if country:
        query = query.filter(Keyword.country == country)
    
    if search:
        query = query.filter(Keyword.keyword.ilike(f"%{search}%"))
    
    if min_volume is not None:
        query = query.filter(Keyword.search_volume >= min_volume)
    
    if max_difficulty is not None:
        query = query.filter(Keyword.keyword_difficulty <= max_difficulty)
    
    if approved_only:
        query = query.filter(Keyword.is_approved == True)
    
    # 排序
    if hasattr(Keyword, sort_by):
        if sort_order.lower() == "desc":
            query = query.order_by(desc(getattr(Keyword, sort_by)))
        else:
            query = query.order_by(getattr(Keyword, sort_by))
    
    # 获取总数
    from sqlalchemy import func
    count_query = select(func.count()).select_from(query.subquery())
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    # 分页查询
    paged_query = query.offset((page - 1) * size).limit(size)
    result = await db.execute(paged_query)
    keywords = result.scalars().all()
    
    total_pages = (total + size - 1) // size
    
    return KeywordListResponse(
        keywords=keywords,
        total=total,
        page=page,
        size=size,
        total_pages=total_pages
    )


@router.get("/{keyword_id}", response_model=KeywordResponse)
async def get_keyword(
    keyword_id: UUID,
    db: AsyncSession = Depends(get_session)
):
    """获取单个关键词详情"""
    
    result = await db.execute(select(Keyword).filter(Keyword.id == keyword_id))
    keyword = result.scalar_one_or_none()
    if not keyword:
        raise HTTPException(status_code=404, detail="关键词不存在")
    
    return keyword


@router.put("/{keyword_id}", response_model=KeywordResponse)
async def update_keyword(
    keyword_id: UUID,
    keyword_data: KeywordUpdate,
    db: AsyncSession = Depends(get_session)
):
    """更新关键词"""
    
    result = await db.execute(select(Keyword).filter(Keyword.id == keyword_id))
    keyword = result.scalar_one_or_none()
    if not keyword:
        raise HTTPException(status_code=404, detail="关键词不存在")
    
    # 更新字段
    update_data = keyword_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(keyword, field, value)
    
    await db.commit()
    await db.refresh(keyword)
    
    return keyword


@router.delete("/{keyword_id}")
async def delete_keyword(
    keyword_id: UUID,
    db: AsyncSession = Depends(get_session)
):
    """删除关键词"""
    
    result = await db.execute(select(Keyword).filter(Keyword.id == keyword_id))
    keyword = result.scalar_one_or_none()
    if not keyword:
        raise HTTPException(status_code=404, detail="关键词不存在")
    
    await db.delete(keyword)
    await db.commit()
    
    return {"message": "关键词已删除"}


@router.post("/analyze", response_model=List[KeywordResponse])
async def analyze_keywords(
    request: KeywordAnalysisRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_session)
):
    """分析关键词并存储到数据库"""
    
    try:
        # 分析关键词
        metrics_list = await keyword_analyzer.analyze_keywords_batch(
            request.keywords,
            request.language,
            request.country
        )
        
        created_keywords = []
        
        for metrics in metrics_list:
            # 检查关键词是否已存在
            result = await db.execute(select(Keyword).filter(
                and_(
                    Keyword.keyword == metrics.keyword,
                    Keyword.language == request.language,
                    Keyword.country == request.country
                )
            ))
            existing = result.scalar_one_or_none()
            
            if existing:
                # 更新现有关键词
                existing.search_volume = metrics.search_volume
                existing.keyword_difficulty = metrics.keyword_difficulty
                existing.cpc = metrics.cpc
                existing.competition = metrics.competition
                existing.search_intent = metrics.search_intent
                existing.zero_click_rate = metrics.zero_click_rate
                existing.trend_score = metrics.trend_score
                existing.trend_data = metrics.trend_data
                existing.serp_quality_score = metrics.serp_quality_score
                existing.serp_analysis = metrics.serp_analysis
                existing.status = "enriched"
                
                # 计算潜力分数
                existing.potential_score = keyword_analyzer.calculate_potential_score(metrics)
                
                created_keywords.append(existing)
            else:
                # 创建新关键词
                keyword = Keyword(
                    keyword=metrics.keyword,
                    language=request.language,
                    country=request.country,
                    search_volume=metrics.search_volume,
                    keyword_difficulty=metrics.keyword_difficulty,
                    cpc=metrics.cpc,
                    competition=metrics.competition,
                    search_intent=metrics.search_intent,
                    zero_click_rate=metrics.zero_click_rate,
                    trend_score=metrics.trend_score,
                    trend_data=metrics.trend_data,
                    serp_quality_score=metrics.serp_quality_score,
                    serp_analysis=metrics.serp_analysis,
                    status="enriched"
                )
                
                # 计算潜力分数
                keyword.potential_score = keyword_analyzer.calculate_potential_score(metrics)
                
                db.add(keyword)
                created_keywords.append(keyword)
        
        await db.commit()
        
        # 刷新对象以获取生成的ID
        for keyword in created_keywords:
            await db.refresh(keyword)
        
        return created_keywords
        
    except Exception as e:
        logger.error(f"Error analyzing keywords: {str(e)}")
        raise HTTPException(status_code=500, detail=f"分析关键词时出错: {str(e)}")


@router.post("/{keyword_id}/calculate-score")
async def calculate_potential_score(
    keyword_id: UUID,
    request: PotentialScoreRequest,
    db: AsyncSession = Depends(get_session)
):
    """重新计算关键词潜力分数"""
    
    result = await db.execute(select(Keyword).filter(Keyword.id == keyword_id))
    keyword = result.scalar_one_or_none()
    if not keyword:
        raise HTTPException(status_code=404, detail="关键词不存在")
    
    # 构建指标对象
    from app.services.keyword_analyzer import KeywordMetrics
    metrics = KeywordMetrics(
        keyword=keyword.keyword,
        search_volume=keyword.search_volume,
        keyword_difficulty=keyword.keyword_difficulty,
        cpc=keyword.cpc,
        competition=keyword.competition,
        search_intent=keyword.search_intent,
        zero_click_rate=keyword.zero_click_rate,
        trend_score=keyword.trend_score,
        trend_data=keyword.trend_data,
        serp_quality_score=keyword.serp_quality_score,
        serp_analysis=keyword.serp_analysis
    )
    
    # 计算新的潜力分数
    weights = {
        "volume": request.volume_weight,
        "difficulty": request.difficulty_weight,
        "cpc": request.cpc_weight,
        "trend": request.trend_weight,
        "serp_quality": request.serp_weight
    }
    
    new_score = keyword_analyzer.calculate_potential_score(metrics, weights)
    
    # 更新关键词
    keyword.potential_score = new_score
    await db.commit()
    
    return {"potential_score": new_score}


@router.post("/{keyword_id}/approve")
async def approve_keyword(
    keyword_id: UUID,
    db: AsyncSession = Depends(get_session)
):
    """批准关键词用于建站"""
    
    result = await db.execute(select(Keyword).filter(Keyword.id == keyword_id))
    keyword = result.scalar_one_or_none()
    if not keyword:
        raise HTTPException(status_code=404, detail="关键词不存在")
    
    keyword.is_approved = True
    keyword.status = "approved"
    await db.commit()
    
    return {"message": "关键词已批准"}


@router.post("/{keyword_id}/reject")
async def reject_keyword(
    keyword_id: UUID,
    db: AsyncSession = Depends(get_session)
):
    """拒绝关键词"""
    
    result = await db.execute(select(Keyword).filter(Keyword.id == keyword_id))
    keyword = result.scalar_one_or_none()
    if not keyword:
        raise HTTPException(status_code=404, detail="关键词不存在")
    
    keyword.is_approved = False
    keyword.status = "rejected"
    await db.commit()
    
    return {"message": "关键词已拒绝"}


# 种子关键词管理

@router.post("/seeds", response_model=SeedKeywordResponse)
async def create_seed_keyword(
    seed_data: SeedKeywordCreate,
    db: AsyncSession = Depends(get_session)
):
    """创建种子关键词"""
    
    seed = SeedKeyword(**seed_data.dict())
    db.add(seed)
    await db.commit()
    await db.refresh(seed)
    
    return seed


@router.get("/seeds", response_model=List[SeedKeywordResponse])
async def list_seed_keywords(
    active_only: bool = Query(True, description="仅显示激活的种子词"),
    category: Optional[str] = Query(None, description="分类筛选"),
    db: AsyncSession = Depends(get_session)
):
    """获取种子关键词列表"""
    
    query = select(SeedKeyword)
    
    if active_only:
        query = query.filter(SeedKeyword.is_active == True)
    
    if category:
        query = query.filter(SeedKeyword.category == category)
    
    query = query.order_by(desc(SeedKeyword.priority), SeedKeyword.created_at)
    
    result = await db.execute(query)
    return result.scalars().all()


@router.post("/seeds/{seed_id}/expand")
async def expand_seed_keyword(
    seed_id: UUID,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_session)
):
    """扩展种子关键词生成相关关键词"""
    
    result = await db.execute(select(SeedKeyword).filter(SeedKeyword.id == seed_id))
    seed = result.scalar_one_or_none()
    if not seed:
        raise HTTPException(status_code=404, detail="种子关键词不存在")
    
    # 扩展关键词
    expanded_keywords = keyword_expander.expand_seed_keywords(
        [seed.root_word],
        seed.category or "tools"
    )
    
    # 如果有自定义修饰词，也使用它们
    if seed.modifiers:
        custom_expanded = keyword_expander.generate_keyword_combinations(
            [seed.root_word],
            seed.modifiers
        )
        expanded_keywords.extend(custom_expanded)
    
    # 去重
    expanded_keywords = list(set(expanded_keywords))
    
    # 创建关键词记录（标记为raw状态）
    created_count = 0
    for kw in expanded_keywords:
        existing_result = await db.execute(select(Keyword).filter(
            and_(
                Keyword.keyword == kw,
                Keyword.language == seed.language,
                Keyword.country == seed.country
            )
        ))
        existing = existing_result.scalar_one_or_none()
        
        if not existing:
            keyword = Keyword(
                keyword=kw,
                language=seed.language,
                country=seed.country,
                status="raw"
            )
            db.add(keyword)
            created_count += 1
    
    # 更新种子词的最后扩展时间
    from datetime import datetime
    seed.last_expanded_at = datetime.utcnow()
    
    await db.commit()
    
    return {
        "message": f"成功扩展并创建了 {created_count} 个新关键词",
        "total_expanded": len(expanded_keywords),
        "created_count": created_count
    }