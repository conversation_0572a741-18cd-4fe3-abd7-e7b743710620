"""网站管理 API 路由"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import desc
from typing import List, Optional
import logging
from uuid import UUID
import os

from app.database import get_session
from app.models import Website, Keyword, WebsiteTask
from app.schemas_keyword import (
    WebsiteCreate, WebsiteUpdate, WebsiteResponse,
    WebsiteTaskResponse
)
from app.services.website_generator import WebsiteGenerator, WebsiteConfig
from app.services.content_ai import ContentAI, WebsiteContentGenerator

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/websites", tags=["websites"])

# 初始化服务
website_generator = WebsiteGenerator()

# ContentAI需要API密钥，这里使用环境变量
openai_api_key = os.getenv("OPENAI_API_KEY")
if openai_api_key:
    content_ai = ContentAI(openai_api_key)
    ai_content_generator = WebsiteContentGenerator(content_ai)
else:
    content_ai = None
    ai_content_generator = None
    logger.warning("OpenAI API key not found. AI content generation will be disabled.")


@router.post("/", response_model=WebsiteResponse)
async def create_website(
    website_data: WebsiteCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_session)
):
    """创建新网站"""
    
    # 检查关键词是否存在
    keyword = db.query(Keyword).filter(Keyword.id == website_data.keyword_id).first()
    if not keyword:
        raise HTTPException(status_code=404, detail="关键词不存在")
    
    # 创建网站记录
    website = Website(**website_data.dict())
    db.add(website)
    db.commit()
    db.refresh(website)
    
    # 启动网站生成任务
    background_tasks.add_task(generate_website_task, str(website.id))
    
    return website


@router.get("/", response_model=List[WebsiteResponse])
async def list_websites(
    keyword_id: Optional[UUID] = None,
    status: Optional[str] = None,
    limit: int = 50,
    db: Session = Depends(get_session)
):
    """获取网站列表"""
    
    query = db.query(Website)
    
    if keyword_id:
        query = query.filter(Website.keyword_id == keyword_id)
    
    if status:
        query = query.filter(Website.status == status)
    
    query = query.order_by(desc(Website.created_at)).limit(limit)
    
    return query.all()


@router.get("/{website_id}", response_model=WebsiteResponse)
async def get_website(
    website_id: UUID,
    db: Session = Depends(get_session)
):
    """获取单个网站详情"""
    
    website = db.query(Website).filter(Website.id == website_id).first()
    if not website:
        raise HTTPException(status_code=404, detail="网站不存在")
    
    return website


@router.put("/{website_id}", response_model=WebsiteResponse)
async def update_website(
    website_id: UUID,
    website_data: WebsiteUpdate,
    db: Session = Depends(get_session)
):
    """更新网站"""
    
    website = db.query(Website).filter(Website.id == website_id).first()
    if not website:
        raise HTTPException(status_code=404, detail="网站不存在")
    
    # 更新字段
    update_data = website_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(website, field, value)
    
    db.commit()
    db.refresh(website)
    
    return website


@router.delete("/{website_id}")
async def delete_website(
    website_id: UUID,
    db: Session = Depends(get_session)
):
    """删除网站"""
    
    website = db.query(Website).filter(Website.id == website_id).first()
    if not website:
        raise HTTPException(status_code=404, detail="网站不存在")
    
    db.delete(website)
    db.commit()
    
    return {"message": "网站已删除"}


@router.post("/{website_id}/regenerate")
async def regenerate_website(
    website_id: UUID,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_session)
):
    """重新生成网站"""
    
    website = db.query(Website).filter(Website.id == website_id).first()
    if not website:
        raise HTTPException(status_code=404, detail="网站不存在")
    
    # 重置状态
    website.status = "pending"
    db.commit()
    
    # 启动重新生成任务
    background_tasks.add_task(generate_website_task, str(website.id))
    
    return {"message": "网站重新生成任务已启动"}


@router.post("/{website_id}/deploy")
async def deploy_website(
    website_id: UUID,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_session)
):
    """部署网站"""
    
    website = db.query(Website).filter(Website.id == website_id).first()
    if not website:
        raise HTTPException(status_code=404, detail="网站不存在")
    
    if website.status != "built":
        raise HTTPException(status_code=400, detail="网站必须先构建完成才能部署")
    
    # 启动部署任务
    background_tasks.add_task(deploy_website_task, str(website.id))
    
    return {"message": "网站部署任务已启动"}


@router.get("/{website_id}/tasks", response_model=List[WebsiteTaskResponse])
async def get_website_tasks(
    website_id: UUID,
    db: Session = Depends(get_session)
):
    """获取网站任务列表"""
    
    website = db.query(Website).filter(Website.id == website_id).first()
    if not website:
        raise HTTPException(status_code=404, detail="网站不存在")
    
    tasks = db.query(WebsiteTask).filter(
        WebsiteTask.website_id == website_id
    ).order_by(desc(WebsiteTask.created_at)).all()
    
    return tasks


@router.post("/batch-generate")
async def batch_generate_websites(
    keyword_ids: List[UUID],
    background_tasks: BackgroundTasks,
    template_id: Optional[str] = None,
    db: Session = Depends(get_session)
):
    """批量生成网站"""
    
    # 检查关键词是否存在
    keywords = db.query(Keyword).filter(Keyword.id.in_(keyword_ids)).all()
    if len(keywords) != len(keyword_ids):
        raise HTTPException(status_code=400, detail="部分关键词不存在")
    
    created_websites = []
    
    for keyword in keywords:
        # 检查是否已有网站
        existing = db.query(Website).filter(Website.keyword_id == keyword.id).first()
        if existing:
            continue
        
        # 创建网站记录
        website = Website(
            keyword_id=keyword.id,
            title=f"{keyword.keyword.title()} Tool",
            description=f"Free online {keyword.keyword.lower()} tool",
            template_id=template_id
        )
        db.add(website)
        db.flush()
        
        created_websites.append(website)
        
        # 启动生成任务
        background_tasks.add_task(generate_website_task, str(website.id))
    
    db.commit()
    
    return {
        "message": f"成功创建 {len(created_websites)} 个网站生成任务",
        "websites": [{"id": str(w.id), "keyword": w.keyword.keyword} for w in created_websites]
    }


# 背景任务函数

async def generate_website_task(website_id: str):
    """网站生成后台任务"""
    
    from app.database import SessionLocal
    db = SessionLocal()
    
    try:
        website = db.query(Website).filter(Website.id == website_id).first()
        if not website:
            logger.error(f"Website {website_id} not found")
            return
        
        keyword = website.keyword
        
        # 更新状态
        website.status = "generating"
        db.commit()
        
        # 准备网站配置
        config = WebsiteConfig(
            keyword=keyword.keyword,
            title=website.title,
            description=website.description or f"Free online {keyword.keyword.lower()} tool",
            tool_type=keyword.tool_category or "tool",
            template_id=website.template_id or "",
            language=keyword.language,
            domain=website.domain
        )
        
        # 如果启用了AI内容生成
        if ai_content_generator:
            try:
                # 生成AI内容
                ai_content = await ai_content_generator.generate_complete_website_content(
                    keyword=keyword.keyword,
                    tool_type=keyword.tool_category or "tool",
                    language=keyword.language
                )
                
                # 格式化内容
                formatted_content = ai_content_generator.format_for_website_generator(ai_content)
                
                # 更新网站内容数据
                website.content_data = ai_content
                website.seo_metadata = formatted_content
                
            except Exception as e:
                logger.error(f"AI content generation failed for website {website_id}: {str(e)}")
                # 继续使用默认内容生成
        
        # 生成网站
        result = website_generator.generate_website(config)
        
        if result["success"]:
            website.status = "built"
            website.github_repo = result.get("project_path")
            website.build_log = f"Generated successfully. Files: {len(result.get('files_generated', []))}"
        else:
            website.status = "failed"
            website.build_log = result.get("error", "Unknown error")
        
        db.commit()
        
        logger.info(f"Website {website_id} generation completed with status: {website.status}")
        
    except Exception as e:
        db.rollback()
        logger.error(f"Website generation task failed for {website_id}: {str(e)}")
        
        # 更新失败状态
        website = db.query(Website).filter(Website.id == website_id).first()
        if website:
            website.status = "failed"
            website.build_log = str(e)
            db.commit()
    
    finally:
        db.close()


async def deploy_website_task(website_id: str):
    """网站部署后台任务"""
    
    from app.database import SessionLocal
    db = SessionLocal()
    
    try:
        website = db.query(Website).filter(Website.id == website_id).first()
        if not website:
            logger.error(f"Website {website_id} not found")
            return
        
        # 更新状态
        website.status = "deploying"
        db.commit()
        
        # 这里应该实现实际的部署逻辑
        # 例如：推送到GitHub，触发Vercel部署等
        
        # 模拟部署过程
        import asyncio
        await asyncio.sleep(10)  # 模拟部署时间
        
        # 部署成功
        website.status = "deployed"
        website.deployment_url = f"https://{website.keyword.keyword.replace(' ', '-').lower()}-tool.vercel.app"
        website.deployed_at = db.query(Website).filter(Website.id == website_id).first().created_at  # 实际应该用当前时间
        
        db.commit()
        
        logger.info(f"Website {website_id} deployed successfully to {website.deployment_url}")
        
    except Exception as e:
        db.rollback()
        logger.error(f"Website deployment task failed for {website_id}: {str(e)}")
        
        # 更新失败状态
        website = db.query(Website).filter(Website.id == website_id).first()
        if website:
            website.status = "deploy_failed"
            website.build_log = f"Deployment failed: {str(e)}"
            db.commit()
    
    finally:
        db.close()