{% extends "base.html" %}

{% block extrahead %}
  {% set title = config.site_name %}
  {% if page and page.meta and page.meta.title %}
    {% set title = title ~ " - " ~ page.meta.title %}
  {% elif page and page.title and not page.is_homepage %}
    {% set title = title ~ " - " ~ page.title %}
  {% endif %}
  <meta property="og:type" content="website" />
  <meta property="og:title" content="{{ title }}" />
  <meta property="og:description" content="{{ config.site_description }}" />
  <meta property="og:url" content="{{ page.canonical_url }}" />
  <meta property="og:image" content="{{ page.canonical_url }}/images/thumbnail.png" />
  <meta property="og:image:type" content="image/png" />
  <meta property="og:image:width" content="1200" />
  <meta property="og:image:height" content="630" />

  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:title" content="{{ title }}" />
  <meta property="twitter:description" content="{{ config.site_description }}" />
  <meta property="twitter:image" content="{{ page.canonical_url }}/images/thumbnail.png" />


{% endblock %}
